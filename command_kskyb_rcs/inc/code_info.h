/*
 * Program Name : code_info.h
 * Comments     : Code Information
 */

/*
 * Process Type
 */
#define MONITOR_TYPE            1
#define LOGGER_TYPE             2
#define SERVER_TYPE             3
#define CLIENT_TYPE             4
#define ANY_TYPE                5

/*
 * Process Status
 */
#define INIT_STATUS             0
#define ACTIVE_STATUS           1
#define INACTIVE_STATUS         2

/*
 * Message Type
 */
#define MONITOR_MSG             1
#define LOGGER_MSG              2
#define TIMER_MSG               3
#define SCHEDULE_MSG            4
#define SHUTDOWN_MSG            5
#define RECEIVE_MSG             10
#define REQ_MSG                 20
#define REP_MSG                 21

/*
 * Sub Message Type
 */
#define DEFAULT_SUB_MSG         0
#define SEND_ERROR_SUB_MSG      1

/*
 * process ....
 */
/* unix offest 47 , linux offset 48 */
#define     PROCESS_NAME_OFFSET     48
#define     PROCESS_CONFIG          "/user/neorcs/command_kskyb_rcs/cfg/conf.table"

#define     SYS_SHM_KEY             0x95000
#define     SYS_SEMA_KEY            0x95000
#define     SYS_MAX_PROCESS         256
#define     CHECK_QUEUE_KEY         0x95000L

#define     PERMS                    0644

#define     FILEPATH_TABLE          "/user/neorcs/command_kskyb_rcs/cfg/conf.table"
#define     FILEPATH_LOG            "/data/log_rcs/"
#define     FILEPATH_BLOCK_EXCEPTION "/user/neorcs/command_kskyb_rcs/cfg/block_exception.txt"

/* Alert2Admin */
#define CALL_WEB	"10.216.243.231"
#define PORT_WEB	80
#define WEB_PAGE	"/alertcall/alertcall.php"

/* END */

