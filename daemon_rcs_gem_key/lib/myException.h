/*
 * myException.h
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#ifndef _myException
#define _myException

#include <string>
using namespace std;

class myException
{
public:
	myException(int, const string&);
	virtual ~myException()
	{
	};

	virtual void response();
	int getErrCode()
	{
		return errorCode;
	}
	string& getErrMsg()
	{
		return errorMsg;
	}

private:
	void initVars();

private:
	int errorCode;
	string errorMsg;
};

#endif
