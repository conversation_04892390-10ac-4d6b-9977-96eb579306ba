#include "alimTalkApi.h"
#include <iconv.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>
#include <algorithm>

char *replaceAll(char *s, const char *olds, const char *news);
void RemoveFirst(char *buf);
void RemoveEnd(char *buf);


void CAlimtalkApi::makeAuthData(int tokenFlag, string username,string password, string refreshToken, string &parameter)
{
	string user_name;
	string pass_word;
	string refresh_token;
	string authFormat;
	
	user_name 		= username.c_str();
	pass_word 		= password.c_str();
	refresh_token	= refreshToken.c_str();
	
	if(tokenFlag == 0)	
	{
		authFormat = "username=";
		authFormat += user_name;
		authFormat += "&password=";
		authFormat += pass_word;
		authFormat += "&grant_type=password";
	}
	else
	{
		authFormat = "refresh_token=";
		authFormat += refresh_token;
		authFormat += "&grant_type=refresh_token";
	}
		
	//authFormat += "\"";
	
	parameter = authFormat;
	
	//cout<<"parameter ["<<parameter<<"]\n"<<endl;
			
}

void CAlimtalkApi::makeRequestMsg(map<string,string> &_mapSend, char* serviceFlag, string &parameter, long long msgid)
{
	string clientMsgId;
	string chatbotId;	
	string messagebaseId;
	string userContact;
	string agencyId;
	string groupId;
	string header;
	string footer;
	string copyAllowed;
	string expiryOption; //20210415
	string title1;
	string title2;
	string title3;
	string title4;
	string title5;
	string title6;
	string description1;
	string description2;
	string description3;
	string description4;
	string description5;
	string description6;
	string media1;
	string media2;
	string media3;
	string media4;
	string media5;
	string media6;
	string button1;
	string button2;
	string button3;
	string button4;
	string button5;
	string button6;
	string button7;
	string button8;
	string button9;
	string button10;
	string button11;
	string button12;
	//202304 추가
	string agencyKey;
	string brandKey;
	
	Json::Value root;
	Json::Value body;
	Json::Value suggestions;
    
	//신규버튼 기능에 의한 추가 
	string button_data_tmp;
	
	
	clientMsgId 	= _mapSend["mms_id"];
	chatbotId		= _mapSend["callback"];
	messagebaseId = _mapSend["mbase_id"];
	userContact 	= _mapSend["dst_addr"];
	groupId     	= _mapSend["group_id"];
	header      	= _mapSend["head"];
	footer      	= _mapSend["footer"];
	copyAllowed 	= _mapSend["copy_allowed"];
	agencyId        = _mapSend["agency_id"];
	expiryOption	= _mapSend["expiry_option"]; //20210415
	agencyKey		= _mapSend["agency_key"];
	brandKey		= _mapSend["brand_key"];
	
	
	root["clientMsgId"]	= clientMsgId; 
	root["chatbotId"]	= chatbotId	 ; 
	root["messagebaseId"]= messagebaseId; 
	root["userContact"] = userContact; 
	if(groupId.length() > 0)
	root["groupId"]     = groupId    ; 
	root["header"]      = header     ; 
	if(footer.length() > 0)
	root["footer"]      = footer     ; 
	root["copyAllowed"] = copyAllowed; 
	if(agencyId.length() > 0)
	root["agencyId"]      = agencyId     ; 
	if(expiryOption.length() > 0)
	root["expiryOption"]      = expiryOption     ; //20210415

	//202304 AgencyKey 추가
	root["agencyKey"] = agencyKey;
	root["brandKey"] = brandKey;
	
	if(strcmp(serviceFlag,"S") == 0)
	{
		description1	= _mapSend["msg_body1"];
		button1     	= _mapSend["button1"];

		char cdescription1[2600+1];
		char cdescription1_utf8[5200+1];

		memset(cdescription1,0x00,sizeof(cdescription1));
		memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));    		

		if(messagebaseId.compare("SS000000") == 0)    
		{
			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));

			body["description"] = cdescription1_utf8;

			root["body"] = body;
			Json::FastWriter writer;
			parameter = writer.write(root);
		}
		else
		{
			//if (messagebaseId.substr(0, 3) == "IBR")
			{			
				// Replace \r\n to \n 				
				Json::Reader reader;
				Json::Value body_r;

				bool parsingSuccessful = reader.parse(description1, body_r);
				if (!parsingSuccessful) {
					cout << "Failed to parse body : " << description1 << endl;
				}
				else
				{
					for (Json::ValueIterator it = body_r.begin(); it != body_r.end(); ++it) {
						const std::string key = it.key().asString();
						Json::Value& value = *it;

						if (value.isString()) {
							string strValue = value.asString();

							// remove '\r' char
                            strValue.erase(std::remove(strValue.begin(), strValue.end(), '\r'), strValue.end());
                            value = strValue;
						}
					}

					Json::FastWriter writer_body;
					writer_body.omitEndingLineFeed();
					
					// FastWriter.write() method transfering '\n' to '\\n'
                    description1 = writer_body.write(body_r);
				}
			}
			
			//check for cell template
			int checkValue = description1.find(":");
			int checkValue2 = description1.find("{");
			int checkValue3 = description1.find("}");

			if(checkValue > 0 && checkValue2 == 0 && checkValue3 > 0)
			{
				string cellBody;

				strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
				euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));

				cellBody = "\"body\":";
				cellBody += cdescription1_utf8;

				Json::FastWriter writer;
				parameter = writer.write(root);

				parameter.insert(parameter.size()-2,",");

				parameter.insert(parameter.size()-2,cellBody);
			}
			else
			{
				strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
				euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));

				body["description"] = cdescription1_utf8;

				root["body"] = body;

				Json::FastWriter writer;
				parameter = writer.write(root);
			}
		}

		if(button1.length()> 0 && messagebaseId.compare("SS000000") == 0)
		{
			char cbutton_data[1300+1];
			memset(cbutton_data,0x00,sizeof(cbutton_data));
			char cbutton_data_utf8[2600+1];
			memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
			string button_data_format;


			button_data_format = "\"buttons\":[{\"suggestions\":[{";
			button_data_format += button1;
			button_data_format += "}]}]";


			strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);

			euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));

			button_data_tmp = cbutton_data_utf8;

			parameter.insert(parameter.size()-2,",");

			parameter.insert(parameter.size()-2,button_data_tmp);

		}
		//cout<<"parameter sms : "<<parameter.c_str()<<"\n"<<endl;
	}	
	else if(strcmp(serviceFlag,"L") == 0)
	{
		title1      	= _mapSend["msg_title1"];
		description1	= _mapSend["msg_body1"];
		button1     	= _mapSend["button1"];
		button2     	= _mapSend["button2"];
		button3     	= _mapSend["button3"];
		
		char ctitle1[60+1];
    	char ctitle1_utf8[120+1];
    	char cdescription1[2600+1];
    	char cdescription1_utf8[5200+1];
    		
    	memset(ctitle1,0x00,sizeof(ctitle1));
    	memset(ctitle1_utf8,0x00,sizeof(ctitle1_utf8));
    	memset(cdescription1,0x00,sizeof(cdescription1));
    	memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));

		if (title1.length() > 0) {
			strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
	    	euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
		}
	    	
    	strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    	euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));

		if(messagebaseId.compare(0, 5, "SL000") == 0) { 
	    	body["title"] = ctitle1_utf8;
	    	body["description"] = cdescription1_utf8;
	    	
	    	root["body"] = body;
			
			Json::FastWriter writer;
			//writer.omitEndingLineFeed();		// "\n" 생략
	        parameter = writer.write(root);
		}
		else {
			// OL00000(LMS 강조형), LBR (LMS템플릿)
			
			// Replace \r\n to \n
			Json::Reader reader;
			Json::Value body_r;

			//bool parsingSuccessful = reader.parse(description1, body_r);
			bool parsingSuccessful = reader.parse(cdescription1_utf8, body_r);
			
			if (parsingSuccessful && body_r.isObject()) {					
				for (Json::ValueIterator it = body_r.begin(); it != body_r.end(); ++it) {
					const std::string key = it.key().asString();
					Json::Value& value = *it;

					if (value.isString()) {
						string strValue = value.asString();

						// remove '\r' char
                        strValue.erase(std::remove(strValue.begin(), strValue.end(), '\r'), strValue.end());
                        value = strValue;
					}
				}	
				root["body"] = body_r;				
			}
			else
			{	
		    	body["title"] = ctitle1_utf8;
		    	body["description"] = cdescription1_utf8;
	    	
		    	root["body"] = body;						
			}		

			Json::FastWriter writer;
			parameter = writer.write(root);
		}
    	
    	if(button1.length()> 0||button2.length()> 0||button3.length()> 0)
		{
    		char cbutton_data[3900+1];
			memset(cbutton_data,0x00,sizeof(cbutton_data));
			char cbutton_data_utf8[7800+1];
			memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
			string button_data_format;
			
			button_data_format = "\"buttons\":[{\"suggestions\":[";
			if(button1.length()> 0)
			{
				button_data_format += "{";
				button_data_format += button1;
				button_data_format += "}";
			}
			if(button1.length()> 0 && button2.length()> 0)
			{
				button_data_format += ",";
			}
			if(button2.length()> 0)
			{
				button_data_format += "{";
				button_data_format += button2;
				button_data_format += "}";
			}
			if((button1.length()> 0 || button2.length()> 0) && button3.length()> 0)
			{	
				button_data_format += ",";
			}
			if(button3.length()> 0)
			{
				button_data_format += "{";
				button_data_format += button3;
				button_data_format += "}";
			}
			button_data_format += "]}]";
			
			strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
			euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
			
			button_data_tmp = cbutton_data_utf8;
			
			parameter.insert(parameter.size()-2,",");
			
			parameter.insert(parameter.size()-2,button_data_tmp);
		}
		//cout<<"parameter lms : "<<parameter.c_str()<<"\n"<<endl;
	}	
	else if(strcmp(serviceFlag,"M") == 0)
	{
		title1      	= _mapSend["msg_title1"];
		title2      	= _mapSend["msg_title2"];
		title3      	= _mapSend["msg_title3"];
		title4      	= _mapSend["msg_title4"];
		title5      	= _mapSend["msg_title5"];
		title6      	= _mapSend["msg_title6"];
		description1	= _mapSend["msg_body1"];
		description2	= _mapSend["msg_body2"];
		description3	= _mapSend["msg_body3"];
		description4	= _mapSend["msg_body4"];
		description5	= _mapSend["msg_body5"];
		description6	= _mapSend["msg_body6"];
		media1      	= _mapSend["file_id1"];
		media2      	= _mapSend["file_id2"];
		media3      	= _mapSend["file_id3"];
		media4      	= _mapSend["file_id4"];
		media5      	= _mapSend["file_id5"];
		media6      	= _mapSend["file_id6"];
		button1     	= _mapSend["button1"];
		button2     	= _mapSend["button2"];
		button3     	= _mapSend["button3"];
		button4     	= _mapSend["button4"];
		button5     	= _mapSend["button5"];
		button6     	= _mapSend["button6"];
		button7     	= _mapSend["button7"];
		button8     	= _mapSend["button8"];
		button9     	= _mapSend["button9"];
		button10    	= _mapSend["button10"];
		button11    	= _mapSend["button11"];
		button12		= _mapSend["button12"];  
		
		//title declare, init
		char ctitle1[60+1];
    	char ctitle1_utf8[120+1];
    	char ctitle2[60+1];
    	char ctitle2_utf8[120+1];
    	char ctitle3[60+1];
    	char ctitle3_utf8[120+1];
    	char ctitle4[60+1];
    	char ctitle4_utf8[120+1];
    	char ctitle5[60+1];
    	char ctitle5_utf8[120+1];
    	char ctitle6[60+1];
    	char ctitle6_utf8[120+1];
    
    	memset(ctitle1,0x00,sizeof(ctitle1));
    	memset(ctitle1_utf8,0x00,sizeof(ctitle1_utf8));
    	memset(ctitle2,0x00,sizeof(ctitle2));
    	memset(ctitle2_utf8,0x00,sizeof(ctitle2_utf8));
    	memset(ctitle3,0x00,sizeof(ctitle3));
    	memset(ctitle3_utf8,0x00,sizeof(ctitle3_utf8));
    	memset(ctitle4,0x00,sizeof(ctitle4));
    	memset(ctitle4_utf8,0x00,sizeof(ctitle4_utf8));
    	memset(ctitle5,0x00,sizeof(ctitle5));
    	memset(ctitle5_utf8,0x00,sizeof(ctitle5_utf8));
    	memset(ctitle6,0x00,sizeof(ctitle6));
    	memset(ctitle6_utf8,0x00,sizeof(ctitle6_utf8));
    	
    	//image file declare, init
    	char cmedia1[100+1];
    	char cmedia1_utf8[200+1];
    	char cmedia2[100+1];
    	char cmedia2_utf8[200+1];
    	char cmedia3[100+1];
    	char cmedia3_utf8[200+1];
    	char cmedia4[100+1];
    	char cmedia4_utf8[200+1];
    	char cmedia5[100+1];
    	char cmedia5_utf8[200+1];
    	char cmedia6[100+1];
    	char cmedia6_utf8[200+1];
    
    	memset(cmedia1,0x00,sizeof(cmedia1));
    	memset(cmedia1_utf8,0x00,sizeof(cmedia1_utf8));
    	memset(cmedia2,0x00,sizeof(cmedia2));
    	memset(cmedia2_utf8,0x00,sizeof(cmedia2_utf8));
    	memset(cmedia3,0x00,sizeof(cmedia3));
    	memset(cmedia3_utf8,0x00,sizeof(cmedia3_utf8));
    	memset(cmedia4,0x00,sizeof(cmedia4));
    	memset(cmedia4_utf8,0x00,sizeof(cmedia4_utf8));
    	memset(cmedia5,0x00,sizeof(cmedia5));
    	memset(cmedia5_utf8,0x00,sizeof(cmedia5_utf8));
    	memset(cmedia6,0x00,sizeof(cmedia6));
    	memset(cmedia6_utf8,0x00,sizeof(cmedia6_utf8));
		
		if(messagebaseId.compare("SMwThT00") == 0||messagebaseId.compare("SMwThM00") == 0||
		   messagebaseId.compare("SMwLhX00") == 0||messagebaseId.compare("SMwRhX00") == 0)
		{
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0) 
    		{   		
				char cdescription1[2600+1];
    			char cdescription1_utf8[5200+1];
    			            	
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description"] = cdescription1_utf8;
    		}
    		
    		strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    		euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		body["media"] = cmedia1_utf8;
    		
    		root["body"] = body;
    		
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0)
			{
    			char cbutton_data[2600+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[5200+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0 && button2.length()> 0)
				{
					button_data_format += ",";
				}
				if(button2.length()> 0)
				{	
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				button_data_format += "]}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms card1 : "<<parameter.c_str()<<"\n"<<endl;	
			
		}
		else if(messagebaseId.compare("CMwMhM0300") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[2600+1];
    			char cdescription1_utf8[5200+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[120+1];
    			char cdescription2_utf8[240+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[120+1];
    			char cdescription3_utf8[240+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0)
			{
    			char cbutton_data[7800+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[15600+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
				button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				button_data_format += "}";	
				
					button_data_format += ",{";
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				button_data_format += "}";
				
				button_data_format += ",{";
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwMhM0300 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwShS0300") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[2600+1];
    			char cdescription1_utf8[5200+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[60+1];
    			char cdescription2_utf8[120+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[60+1];
    			char cdescription3_utf8[120+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0)
			{
    			char cbutton_data[7800+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[15600+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
				button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
					button_data_format += "},{";
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
					
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwShS0300 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwMhM0400") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[120+1];
    			char cdescription1_utf8[240+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[120+1];
    			char cdescription2_utf8[240+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[120+1];
    			char cdescription3_utf8[240+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[120+1];
    			char cdescription4_utf8[240+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0)
			{
    			char cbutton_data[10400+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[20800+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
					
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
			
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwMhM0400 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwShS0400") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[60+1];
    			char cdescription1_utf8[120+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[60+1];
    			char cdescription2_utf8[120+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[60+1];
    			char cdescription3_utf8[120+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
			//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[60+1];
    			char cdescription4_utf8[120+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0)
			{
    			char cbutton_data[10400+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[20800+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
					
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
			
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwShS0400 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwMhM0500") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[120+1];
    			char cdescription1_utf8[240+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[120+1];
    			char cdescription2_utf8[240+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[120+1];
    			char cdescription3_utf8[240+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[120+1];
    			char cdescription4_utf8[240+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		//card5
    		if(title5.length() > 0)
			{
				strncpy(ctitle5, title5.c_str(),sizeof(ctitle5)-1);
    			euckrToUtf8(ctitle5, ctitle5_utf8, sizeof(ctitle5_utf8));
			
				body["title5"] = ctitle5_utf8;
    		}
			
			if(description5.length() > 0)
			{
    			char cdescription5[120+1];
    			char cdescription5_utf8[240+1];
    			
    			memset(cdescription5,0x00,sizeof(cdescription5));
    			memset(cdescription5_utf8,0x00,sizeof(cdescription5_utf8));
    		
	    		strncpy(cdescription5, description5.c_str(),sizeof(cdescription5)-1);
    			euckrToUtf8(cdescription5, cdescription5_utf8, sizeof(cdescription5_utf8));
			
				body["description5"] = cdescription5_utf8;
    		}	
			
    		if(media5.length() > 0)
			{
	    		strncpy(cmedia5, media5.c_str(),sizeof(cmedia5)-1);
    			euckrToUtf8(cmedia5, cmedia5_utf8, sizeof(cmedia5_utf8));
    		
    			body["media5"] = cmedia5_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0||
    			button9.length()> 0||button10.length()> 0)
			{
    			char cbutton_data[13000+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[26000+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
					
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
					
				button_data_format += "},{";
				
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				
				button_data_format += "},{";
				
				if(button9.length()> 0||button10.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button9.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button9;
					button_data_format += "}";
				}
				if(button9.length()> 0 && button10.length()> 0)
					button_data_format += ",";
				if(button10.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button10;
					button_data_format += "}";
				}
				if(button9.length()> 0 || button10.length()> 0)
					button_data_format += "]";		
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwMhM0500 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwShS0500") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[60+1];
    			char cdescription1_utf8[120+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[60+1];
    			char cdescription2_utf8[120+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[60+1];
    			char cdescription3_utf8[120+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[60+1];
    			char cdescription4_utf8[120+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		//card5
    		if(title5.length() > 0)
			{
				strncpy(ctitle5, title5.c_str(),sizeof(ctitle5)-1);
    			euckrToUtf8(ctitle5, ctitle5_utf8, sizeof(ctitle5_utf8));
			
				body["title5"] = ctitle5_utf8;
    		}
			
			if(description5.length() > 0)
			{
    			char cdescription5[60+1];
    			char cdescription5_utf8[120+1];
    			
    			memset(cdescription5,0x00,sizeof(cdescription5));
    			memset(cdescription5_utf8,0x00,sizeof(cdescription5_utf8));
    		
	    		strncpy(cdescription5, description5.c_str(),sizeof(cdescription5)-1);
    			euckrToUtf8(cdescription5, cdescription5_utf8, sizeof(cdescription5_utf8));
			
				body["description5"] = cdescription5_utf8;
    		}	
			
    		if(media5.length() > 0)
			{
	    		strncpy(cmedia5, media5.c_str(),sizeof(cmedia5)-1);
    			euckrToUtf8(cmedia5, cmedia5_utf8, sizeof(cmedia5_utf8));
    		
    			body["media5"] = cmedia5_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0||
    			button9.length()> 0||button10.length()> 0)
			{
    			char cbutton_data[13000+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[26000+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
					
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
					
				button_data_format += "},{";
				
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				
				button_data_format += "},{";
				
				if(button9.length()> 0||button10.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button9.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button9;
					button_data_format += "}";
				}
				if(button9.length()> 0 && button10.length()> 0)
					button_data_format += ",";
				if(button10.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button10;
					button_data_format += "}";
				}
				if(button9.length()> 0 || button10.length()> 0)
					button_data_format += "]";		
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwShS0500 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwMhM0600") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[120+1];
    			char cdescription1_utf8[240+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[120+1];
    			char cdescription2_utf8[240+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[120+1];
    			char cdescription3_utf8[240+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[120+1];
    			char cdescription4_utf8[240+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		//card5
    		if(title5.length() > 0)
			{
				strncpy(ctitle5, title5.c_str(),sizeof(ctitle5)-1);
    			euckrToUtf8(ctitle5, ctitle5_utf8, sizeof(ctitle5_utf8));
			
				body["title5"] = ctitle5_utf8;
    		}
			
			if(description5.length() > 0)
			{
    			char cdescription5[120+1];
    			char cdescription5_utf8[240+1];
    			
    			memset(cdescription5,0x00,sizeof(cdescription5));
    			memset(cdescription5_utf8,0x00,sizeof(cdescription5_utf8));
    		
	    		strncpy(cdescription5, description5.c_str(),sizeof(cdescription5)-1);
    			euckrToUtf8(cdescription5, cdescription5_utf8, sizeof(cdescription5_utf8));
			
				body["description5"] = cdescription5_utf8;
    		}	
			
    		if(media5.length() > 0)
			{
	    		strncpy(cmedia5, media5.c_str(),sizeof(cmedia5)-1);
    			euckrToUtf8(cmedia5, cmedia5_utf8, sizeof(cmedia5_utf8));
    		
    			body["media5"] = cmedia5_utf8;
    		}
    		
    		//card6
    		if(title6.length() > 0)
			{
				strncpy(ctitle6, title6.c_str(),sizeof(ctitle6)-1);
    			euckrToUtf8(ctitle6, ctitle6_utf8, sizeof(ctitle6_utf8));
			
				body["title6"] = ctitle6_utf8;
    		}
			
			if(description6.length() > 0)
			{
    			char cdescription6[120+1];
    			char cdescription6_utf8[240+1];
    			
    			memset(cdescription6,0x00,sizeof(cdescription6));
    			memset(cdescription6_utf8,0x00,sizeof(cdescription6_utf8));
    		
	    		strncpy(cdescription6, description6.c_str(),sizeof(cdescription6)-1);
    			euckrToUtf8(cdescription6, cdescription6_utf8, sizeof(cdescription6_utf8));
			
				body["description6"] = cdescription6_utf8;
    		}	
			
    		if(media6.length() > 0)
			{
	    		strncpy(cmedia6, media6.c_str(),sizeof(cmedia6)-1);
    			euckrToUtf8(cmedia6, cmedia6_utf8, sizeof(cmedia6_utf8));
    		
    			body["media6"] = cmedia6_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0||
    			button9.length()> 0||button10.length()> 0||
    			button11.length()> 0||button12.length()> 0)
			{
    			char cbutton_data[15600+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[31200+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				
				button_data_format += "},{";
				
				if(button9.length()> 0||button10.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button9.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button9;
					button_data_format += "}";
				}
				if(button9.length()> 0 && button10.length()> 0)
					button_data_format += ",";
				if(button10.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button10;
					button_data_format += "}";
				}
				if(button9.length()> 0 || button10.length()> 0)
					button_data_format += "]";		
				
				button_data_format += "},{";
				
				if(button11.length()> 0||button12.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button11.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button11;
					button_data_format += "}";
				}
				if(button11.length()> 0 && button12.length()> 0)
					button_data_format += ",";
				if(button12.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button12;
					button_data_format += "}";
				}
				if(button11.length()> 0 || button12.length()> 0)
					button_data_format += "]";	
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwMhM0600 : "<<parameter.c_str()<<"\n"<<endl;	
		}
		else if(messagebaseId.compare("CMwShS0600") == 0)
		{
			//card1
			if(title1.length() > 0)
			{
				strncpy(ctitle1, title1.c_str(),sizeof(ctitle1)-1);
    			euckrToUtf8(ctitle1, ctitle1_utf8, sizeof(ctitle1_utf8));
    			
    			body["title1"] = ctitle1_utf8;
    		}
    		
    		if(description1.length() > 0)
			{	
    			char cdescription1[60+1];
    			char cdescription1_utf8[120+1];
    			
    			memset(cdescription1,0x00,sizeof(cdescription1));
    			memset(cdescription1_utf8,0x00,sizeof(cdescription1_utf8));
    			
    			strncpy(cdescription1, description1.c_str(),sizeof(cdescription1)-1);
    			euckrToUtf8(cdescription1, cdescription1_utf8, sizeof(cdescription1_utf8));
    			
    			body["description1"] = cdescription1_utf8;
    		}
    		
    		if(media1.length() > 0)
			{	
    			strncpy(cmedia1, media1.c_str(),sizeof(cmedia1)-1);
    			euckrToUtf8(cmedia1, cmedia1_utf8, sizeof(cmedia1_utf8));
    		
    			body["media1"] = cmedia1_utf8;
    		}
    		
    		//card2
    		if(title2.length() > 0)
			{
    			strncpy(ctitle2, title2.c_str(),sizeof(ctitle2)-1);
    			euckrToUtf8(ctitle2, ctitle2_utf8, sizeof(ctitle2_utf8));
    		
    			body["title2"] = ctitle2_utf8;
    		}
    		
    		if(description2.length() > 0)
			{
    			char cdescription2[60+1];
    			char cdescription2_utf8[120+1];
    			
    			memset(cdescription2,0x00,sizeof(cdescription2));
    			memset(cdescription2_utf8,0x00,sizeof(cdescription2_utf8));
				
				strncpy(cdescription2, description2.c_str(),sizeof(cdescription2)-1);
    			euckrToUtf8(cdescription2, cdescription2_utf8, sizeof(cdescription2_utf8));
				
				body["description2"] = cdescription2_utf8;
    		}
    		
    		if(media2.length() > 0)
			{
    			strncpy(cmedia2, media2.c_str(),sizeof(cmedia2)-1);
    			euckrToUtf8(cmedia2, cmedia2_utf8, sizeof(cmedia2_utf8));
    			
    			body["media2"] = cmedia2_utf8;
			}
			
			//card3
    		if(title3.length() > 0)
			{
				strncpy(ctitle3, title3.c_str(),sizeof(ctitle3)-1);
    			euckrToUtf8(ctitle3, ctitle3_utf8, sizeof(ctitle3_utf8));
			
				body["title3"] = ctitle3_utf8;
    		}
			
			if(description3.length() > 0)
			{
    			char cdescription3[60+1];
    			char cdescription3_utf8[120+1];
    			
    			memset(cdescription3,0x00,sizeof(cdescription3));
    			memset(cdescription3_utf8,0x00,sizeof(cdescription3_utf8));
    		
	    		strncpy(cdescription3, description3.c_str(),sizeof(cdescription3)-1);
    			euckrToUtf8(cdescription3, cdescription3_utf8, sizeof(cdescription3_utf8));
			
				body["description3"] = cdescription3_utf8;
    		}	
			
    		if(media3.length() > 0)
			{
	    		strncpy(cmedia3, media3.c_str(),sizeof(cmedia3)-1);
    			euckrToUtf8(cmedia3, cmedia3_utf8, sizeof(cmedia3_utf8));
    		
    			body["media3"] = cmedia3_utf8;
    		}
    		
    		//card4
    		if(title4.length() > 0)
			{
				strncpy(ctitle4, title4.c_str(),sizeof(ctitle4)-1);
    			euckrToUtf8(ctitle4, ctitle4_utf8, sizeof(ctitle4_utf8));
			
				body["title4"] = ctitle4_utf8;
    		}
			
			if(description4.length() > 0)
			{
    			char cdescription4[60+1];
    			char cdescription4_utf8[120+1];
    			
    			memset(cdescription4,0x00,sizeof(cdescription4));
    			memset(cdescription4_utf8,0x00,sizeof(cdescription4_utf8));
    		
	    		strncpy(cdescription4, description4.c_str(),sizeof(cdescription4)-1);
    			euckrToUtf8(cdescription4, cdescription4_utf8, sizeof(cdescription4_utf8));
			
				body["description4"] = cdescription4_utf8;
    		}	
			
    		if(media4.length() > 0)
			{
	    		strncpy(cmedia4, media4.c_str(),sizeof(cmedia4)-1);
    			euckrToUtf8(cmedia4, cmedia4_utf8, sizeof(cmedia4_utf8));
    		
    			body["media4"] = cmedia4_utf8;
    		}
    		
    		//card5
    		if(title5.length() > 0)
			{
				strncpy(ctitle5, title5.c_str(),sizeof(ctitle5)-1);
    			euckrToUtf8(ctitle5, ctitle5_utf8, sizeof(ctitle5_utf8));
			
				body["title5"] = ctitle5_utf8;
    		}
			
			if(description5.length() > 0)
			{
    			char cdescription5[60+1];
    			char cdescription5_utf8[120+1];
    			
    			memset(cdescription5,0x00,sizeof(cdescription5));
    			memset(cdescription5_utf8,0x00,sizeof(cdescription5_utf8));
    		
	    		strncpy(cdescription5, description5.c_str(),sizeof(cdescription5)-1);
    			euckrToUtf8(cdescription5, cdescription5_utf8, sizeof(cdescription5_utf8));
			
				body["description5"] = cdescription5_utf8;
    		}	
			
    		if(media5.length() > 0)
			{
	    		strncpy(cmedia5, media5.c_str(),sizeof(cmedia5)-1);
    			euckrToUtf8(cmedia5, cmedia5_utf8, sizeof(cmedia5_utf8));
    		
    			body["media5"] = cmedia5_utf8;
    		}
    		
    		//card6
    		if(title6.length() > 0)
			{
				strncpy(ctitle6, title6.c_str(),sizeof(ctitle6)-1);
    			euckrToUtf8(ctitle6, ctitle6_utf8, sizeof(ctitle6_utf8));
			
				body["title6"] = ctitle6_utf8;
    		}
			
			if(description6.length() > 0)
			{
    			char cdescription6[60+1];
    			char cdescription6_utf8[120+1];
    			
    			memset(cdescription6,0x00,sizeof(cdescription6));
    			memset(cdescription6_utf8,0x00,sizeof(cdescription6_utf8));
    		
	    		strncpy(cdescription6, description6.c_str(),sizeof(cdescription6)-1);
    			euckrToUtf8(cdescription6, cdescription6_utf8, sizeof(cdescription6_utf8));
			
				body["description6"] = cdescription6_utf8;
    		}	
			
    		if(media6.length() > 0)
			{
	    		strncpy(cmedia6, media6.c_str(),sizeof(cmedia6)-1);
    			euckrToUtf8(cmedia6, cmedia6_utf8, sizeof(cmedia6_utf8));
    		
    			body["media6"] = cmedia6_utf8;
    		}
    		
    		root["body"] = body;
			
    		Json::FastWriter writer;
        	parameter = writer.write(root);
    		
    		if(button1.length()> 0||button2.length()> 0||
    			button3.length()> 0||button4.length()> 0||
    			button5.length()> 0||button6.length()> 0||
    			button7.length()> 0||button8.length()> 0||
    			button9.length()> 0||button10.length()> 0||
    			button11.length()> 0||button12.length()> 0)
			{
    			char cbutton_data[15600+1];
				memset(cbutton_data,0x00,sizeof(cbutton_data));
				char cbutton_data_utf8[31200+1];
				memset(cbutton_data_utf8,0x00,sizeof(cbutton_data_utf8));
				string button_data_format;
				
				button_data_format = "\"buttons\":[{";
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button1.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button1;
					button_data_format += "}";
				}
				if(button1.length()> 0&&button2.length()> 0)
					button_data_format += ",";
				if(button2.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button2;
					button_data_format += "}";
				}
				if(button1.length()> 0||button2.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button3.length()> 0||button4.length()> 0)					
					button_data_format += "\"suggestions\":[";
				if(button3.length()> 0)
				{					
					button_data_format += "{";
					button_data_format += button3;
					button_data_format += "}";
				}
				if(button3.length()> 0&&button4.length()> 0)
					button_data_format += ",";
				if(button4.length()> 0)		
				{	button_data_format += "{";
					button_data_format += button4;
					button_data_format += "}";
				}	
				if(button3.length()> 0||button4.length()> 0)
				button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button5.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button5;
					button_data_format += "}";
				}
				if(button5.length()> 0&&button6.length()> 0)
				button_data_format += ",";
				if(button6.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button6;
					button_data_format += "}";
				}
				if(button5.length()> 0||button6.length()> 0)
					button_data_format += "]";
				
				button_data_format += "},{";
				
				if(button7.length()> 0||button8.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button7.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button7;
					button_data_format += "}";
				}
				if(button7.length()> 0 && button8.length()> 0)
					button_data_format += ",";
				if(button8.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button8;
					button_data_format += "}";
				}
				if(button7.length()> 0 || button8.length()> 0)
					button_data_format += "]";	
				
				button_data_format += "},{";
				
				if(button9.length()> 0||button10.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button9.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button9;
					button_data_format += "}";
				}
				if(button9.length()> 0 && button10.length()> 0)
					button_data_format += ",";
				if(button10.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button10;
					button_data_format += "}";
				}
				if(button9.length()> 0 || button10.length()> 0)
					button_data_format += "]";		
				
				button_data_format += "},{";
				
				if(button11.length()> 0||button12.length()> 0)
					button_data_format += "\"suggestions\":[";
				if(button11.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button11;
					button_data_format += "}";
				}
				if(button11.length()> 0 && button12.length()> 0)
					button_data_format += ",";
				if(button12.length()> 0)
				{
					button_data_format += "{";
					button_data_format += button12;
					button_data_format += "}";
				}
				if(button11.length()> 0 || button12.length()> 0)
					button_data_format += "]";	
				button_data_format += "}]";
				
				strncpy(cbutton_data, button_data_format.c_str(), sizeof(cbutton_data)-1);
				
				euckrToUtf8(cbutton_data, cbutton_data_utf8, sizeof(cbutton_data_utf8));
				
				button_data_tmp = cbutton_data_utf8;
				
				parameter.insert(parameter.size()-2,",");
			
				parameter.insert(parameter.size()-2,button_data_tmp);
				
			}
			//cout<<"parameter mms CMwShS0600 : "<<parameter.c_str()<<"\n"<<endl;	
		}
	}
}

void CAlimtalkApi::makePollingRequestMsg(string channelKey, string &parameter)
{
	string channel_key;
	
	channel_key = channelKey.c_str();
    		
	Json::Value root;
	
	root["channel_key"] = channel_key.c_str();
	
	Json::FastWriter writer;
   	parameter = writer.write(root);		
}

int CAlimtalkApi::parsingResponse(string response, ST_TALK_RES &res)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse response msg"<<endl;
		return -1;
	}	

	res.received_at = root.get("received_at", "").asString();
	res.code = root.get("code", "").asString();

	if(res.code != "0000")
	{
		res.message = root.get("message", "").asString(); 	
	}

	return 0;
}

int CAlimtalkApi::parsingTokenResponse(string response, ST_TOKEN_RES &res)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"parsingTokenResponse Failed to parse response msg"<<endl;
		return -1;
	}	

	res.access_token = root.get("access_token", "").asString();
	res.token_type = root.get("token_type", "").asString();
	res.refresh_token = root.get("refresh_token", "").asString();
	res.expires_in = root.get("expires_in", "").asString();
	res.scope = root.get("scope", "").asString();

	return 0;
}

int CAlimtalkApi::parsingTokenErrResponse(string response, ST_TOKEN_ERR &err)
{
	Json::Value root;
	Json::Reader reader;

	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"parsingTokeneRRResponse Failed to parse response msg"<<endl;
		return -1;
	}	

	err.error = root.get("error", "").asString();
	err.error_description = root.get("error_description", "").asString();

	return 0;
}

int CAlimtalkApi::parsingMediaResponse(string response, ST_MEDIA_RES &res)
{
	Json::Value root;
	Json::Value dataValue;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse polling response msg"<<endl;
		return -1;
	}	
	
	if(response.find("code") > 0)
	res.code =      root.get("code", "").asString();
	if(response.find("message") > 0)
	res.message =      root.get("message", "").asString();
	
	if(response.find("data") > 0)
	{
		const Json::Value data = root["data"];
			 	
		res.fileId = data.get("fileId", "").asString();
		res.expiryDate = data.get("expiryDate", "").asString();
	}
	
	return 0;
}

int CAlimtalkApi::parsingMediaErrResponse(string response, ST_MEDIA_ERR_RES &res)
{
	Json::Value root;
	Json::Value dataValue;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse polling response msg"<<endl;
		return -1;
	}	
	
	if(response.find("code") > 0)
	res.code =      root.get("code", "").asString();
	if(response.find("message") > 0)
	res.message =      root.get("message", "").asString();
	
	return 0;
}

int CAlimtalkApi::parsingSendResponse(string response, ST_RCS_SEND_RES &res)
{
	Json::Value root;
	Json::Value dataValue;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse polling response msg"<<endl;
		return -1;
	}	
	
	if(response.find("code") > 0)
	{
		res.code	=	root.get("code", "").asString();
	}
	res.message =	root.get("message", "").asString();

	return 0;
}

int CAlimtalkApi::parsingReportResponse(string response, ST_RCS_RESULT_RES &res 
	                             , ST_RESULT_REPORT * _report 
	                             , int & _reportSize)
{
	Json::Value root;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse result response msg"<<endl;
		return -1;
	}	

	//bool isValidJson = !root.isNull() && (root.isObject() || root.isArray());
	bool isValidJson = !root.isNull() && root.isObject();

#if (DEBUG >= 5)
	cout << "root.isNull() [" << root.isNull() << "]" << endl;
	cout << "root.isObject() [" << root.isObject() << "]" << endl;	
	cout << "root.isArray() [" << root.isArray() << "]" << endl;
	cout << "isValidJson [" << isValidJson << "]" << endl;
#endif
	
	if (!isValidJson) {
		cout << "Invalid JSON format" << endl;
		return -1;
	}	
	
	int msgCount = 0;
	//////////////////////////////////////////////////
  	// parse json array(response key in response Json)
	//const Json::Value responseRoot_t = root["results"];
	//const Json::Value & report_msgs = responseRoot_t["report"];
	const Json::Value & report_msgs = root["results"];

	//cout << "report_msgs.isArray() [" << report_msgs.isArray() << "]" << endl;
	

	if (report_msgs.isArray()) {
	
		for (Json::ValueConstIterator it = report_msgs.begin();
		    it != report_msgs.end(); ++it) 
		{
			const Json::Value & report = *it;
		
			_report[msgCount].result = report.get("result", "").asString();
			_report[msgCount].message = report.get("message", "").asString();
			_report[msgCount].clientMsgId = report.get("clientMsgId", "").asString();
			_report[msgCount].telecom = report.get("telecom", "").asString();
			_report[msgCount].date = report.get("date", "").asString();
			++msgCount; //	check report length
		}
	}
	else {
		cout << "Error: 'results' is not an array" << endl;
		return -1;
	}
	
	_reportSize = msgCount;
	msgCount = 0;

	return 0;
}

int CAlimtalkApi::parsingPollingMsgResponse(string response, ST_TALK_POLLING_RES &res\
	                             , ST_POLLING_SUCCESS * _success, ST_POLLING_FAIL * _fail\
	                             , int & _successSize, int & _failSize)
{
	Json::Value root;
	Json::Reader reader;

  //////////////////////////////////////////////////
  // parse polling response msg
	bool parsingSuccessful = reader.parse(response, root);
	if(!parsingSuccessful)
	{
		cout<<"Failed to parse polling response msg"<<endl;
		return -1;
	}	

	res.code = root.get("code", "").asString();
	if(res.code != "0000")
	{
		res.message = root.get("message", "").asString(); 	
	}

	res.response_id  = root.get("response_id",  "").asString();
	res.responsed_at = root.get("responsed_at", "").asString();

	int msgCount = 0;

  //////////////////////////////////////////////////
  // parse json array(response key in response Json)
	const Json::Value responseRoot_t = root["response"];
	const Json::Value & success_msgs = responseRoot_t["success"];
	for( Json::ValueConstIterator it = success_msgs.begin(); \
	      it != success_msgs.end(); ++it)
  {
  	const Json::Value & success = *it;

  	_success[ msgCount].sn          = success.get( "serial_number","").asString();
  	_success[ msgCount].status      = success.get( "status","").asString();
  	_success[ msgCount].received_at = success.get( "received_at","").asString();
  	++msgCount; //  check success length
  }	

	_successSize = msgCount;
	msgCount = 0;

	const Json::Value & fail_msgs = responseRoot_t["fail"];
	for( Json::ValueConstIterator it = fail_msgs.begin(); \
	      it != fail_msgs.end(); ++it)
  {
  	const Json::Value & fail = *it;

  	_fail[ msgCount].sn          = fail.get( "serial_number","").asString();
  	_fail[ msgCount].status      = fail.get( "status","").asString();
  	_fail[ msgCount].received_at = fail.get( "received_at","").asString();
  	_fail[ msgCount].message     = fail.get( "message","").asString();
  	
  	++msgCount; // check fail length
  }
	_failSize = msgCount;

	return 0;
}

int CAlimtalkApi::euckrToUtf8(char *source, char *dest, int dest_size)
{
	iconv_t it;
	char *pout;
	size_t in_size, out_size;

	it = iconv_open("UTF-8", "EUC-KR");
	in_size = strlen(source);
	out_size = dest_size;
	pout = dest;

	if(iconv(it, &source, &in_size, &pout, &out_size) < 0)
		return -1;


	iconv_close(it);
	
	//20180829 block cout 
	//cout<<"source : \n"<<source<<endl;
	//cout<<"dest : \n"<<dest<<endl;
	
	return (pout - dest);	
}

int CAlimtalkApi::utf8ToEuckr(char *source, char *dest, int dest_size)
{
	iconv_t it;
	char *pout;
	size_t in_size, out_size;

	it = iconv_open("EUC-KR", "UTF-8");
	in_size = strlen(source);
	out_size = dest_size;
	pout = dest;

	if(iconv(it, &source, &in_size, &pout, &out_size) < 0)
		return -1;


	iconv_close(it);
	
	//20180829 block cout 
	//cout<<"source : \n"<<source<<endl;
	//cout<<"dest : \n"<<dest<<endl;
	
	return (pout - dest);	
}

void CAlimtalkApi::makeDateString(const string orgDate, string &dateString)
{
	char yyyy[4+1]; char mm[2+1]; char dd[2+1];
	char hh[2+1]; char mi[2+1]; char ss[2+1];

	int ret = 0;
	ret = sscanf(orgDate.c_str(), "%4s-%2s-%2s %2s:%2s:%2s", yyyy, mm, dd, hh, mi, ss);
	if(ret < 0)
	{
		throw -1;
	}

	dateString.append(yyyy);
	dateString.append(mm);
	dateString.append(dd);
	dateString.append(hh);
	dateString.append(mi);
	dateString.append(ss);

}


char *replaceAll(char *s, const char *olds, const char *news) {
  char *result, *sr;
  size_t i, count = 0;
  size_t oldlen = strlen(olds); if (oldlen < 1) return s;
  size_t newlen = strlen(news);


  if (newlen != oldlen) {
    for (i = 0; s[i] != '\0';) {
      if (memcmp(&s[i], olds, oldlen) == 0) count++, i += oldlen;
      else i++;
    }
  } else i = strlen(s);


  result = (char *) malloc(i + 1 + count * (newlen - oldlen));
  if (result == NULL) return NULL;


  sr = result;
  while (*s) {
    if (memcmp(s, olds, oldlen) == 0) {
      memcpy(sr, news, newlen);
      sr += newlen;
      s  += oldlen;
    } else *sr++ = *s++;
  }
  *sr = '\0';

  return result;
}

void RemoveFirst(char *buf)
{
    int i = 0;
    for (i = 1; buf[i]; i++)//buf[i]가 참(널문자가 아님)이면 반복하여라.
    {
        buf[i - 1] = buf[i]; //buf[i] 문자를 buf[i-1]로 이동
    }
    //현재 i는 널문자가 있는 위치, i-1은 마지막 문자 위치
    buf[i - 1] = '\0';
}


void RemoveEnd(char *buf)
{
    int i = 0;    
    while (buf[i])//buf[i]가 참(널문자가 아님)이면 반복하여라.
    {
        i++;
    }
    //현재 i는 널문자가 있는 위치, i-1은 마지막 문자 위치
    buf[i - 1] = '\0';
}
