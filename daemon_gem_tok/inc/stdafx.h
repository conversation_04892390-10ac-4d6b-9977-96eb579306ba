/*****************************************************************************
 * File Name   : stdafx.h
 * Author      : kskyb.com
 * Date        : 2008.09.08
 * Description : Common Header & Define
 *****************************************************************************/
#ifndef _STDAFX_H_
#define _STDAFX_H_

#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <signal.h>
#include <unistd.h>

#include <sys/socket.h>
#include <arpa/inet.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

#define TRUE	1
#define FALSE	0
#define true	1
#define false	0

#define CCL(a)	memset(a,0x00,sizeof(a))
#define QPERM 0644

#endif //_STDAFX_H_
