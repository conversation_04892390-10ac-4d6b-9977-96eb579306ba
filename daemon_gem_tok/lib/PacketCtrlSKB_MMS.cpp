/*
 * PacketCtrlSKB.cpp
 *
 *  Created on: 2011. 07. 28.
 *      Author: Administrator
 */

#include "PacketCtrlSKB_MMS.h"
#include <cstdio>
#include <cstring>
#include <cstdlib>
#include <time.h>

char tmpLog2[1024];
char tmpLog[1024*1024+1];
void log2(char *buf, int st, int err);
void prt(char *log, char *name, int idx, int size);

void CPacketCtrlSKB::SetLeftEmptyZero(char* pSource, int nSourceSize)
{
	int nSize=0;
	int i;

	for (i=0; i<nSourceSize; i++)
	{
		if (pSource[i] == (char)0x30) nSize++;
		else break;
	}

	//문자열 우측 이동
	for (i=0; i<nSourceSize; i++)
	{
		if(i==nSourceSize-nSize) break;
		pSource[i] = pSource[nSourceSize-nSize+i];
		pSource[nSourceSize-nSize+i]=(char)0x00;
	}
}

void CPacketCtrlSKB::SetLeftFillUpZero(char* pSource, int nSourceSize)
{
	int nSize=0;
	int i;

	for (i=0; i<nSourceSize; i++)
	{
		if (pSource[i] != (char)0x00) nSize++;
	}

	//문자열 우측 이동
	for (i=0; i<nSourceSize; i++)
	{
		if(i==nSize) break;
		pSource[nSourceSize-1-i] = pSource[nSize-1-i];
	}

	// 슷자 0 삽입
	for (i=0; i<nSourceSize-nSize; i++)
	{
		pSource[i]=(char)0x30;
	}
}

void CPacketCtrlSKB::SetFillUpSpace(char* pSource, int nSourceSize)
{
	int i;

	for (i=0; i<nSourceSize; i++)
	{
		if (pSource[i] == (char)0x00) pSource[i]=(char)0x20;	// 스페이스(공백) 삽입
	}
}

void CPacketCtrlSKB::SetReplaceTel(char* pSource, int nSourceSize)
{
	char pDest[nSourceSize];
	int i, j;
	memset(pDest, 0x00, nSourceSize);

	for(i=0,j=0;i<nSourceSize;i++)
	{
		//HYPHEN-MINUS, SPACE 제거
		if (!(pSource[i] == (char)0x2d || pSource[i] == (char)0x20))
		{
			pDest[j++] = pSource[i];
		}
	}

	memset(pSource, 0x00, nSourceSize);
	strcpy(pSource, pDest);
}

int CPacketCtrlSKB::getMsg_AUTH_BIND_REQ(char* pBuff, char* id, char* aid, char* pw, unsigned long tid)
{
	AUTH_BIND_REQ BindSnd;
	memset((char*)&BindSnd, 0x00, sizeof(AUTH_BIND_REQ));

	sprintf(BindSnd.header.szJobCode, "S1");
	sprintf(BindSnd.header.szInvoketype, "00");
	sprintf(BindSnd.header.szVersion, "02.10.00");
	sprintf(BindSnd.header.szClientType, "06");
	sprintf(BindSnd.header.szClientVersion, "02.00.00");
	sprintf(BindSnd.header.szTID, "%lu", tid);
	sprintf(BindSnd.header.szBodySize, "64");

	SetFillUpSpace(BindSnd.header.szJobCode, 2);
	SetFillUpSpace(BindSnd.header.szInvoketype, 2);
	SetFillUpSpace(BindSnd.header.szVersion, 8);
	SetFillUpSpace(BindSnd.header.szClientType, 2);
	SetFillUpSpace(BindSnd.header.szClientVersion, 8);
	SetLeftFillUpZero(BindSnd.header.szTID, 10);
	SetLeftFillUpZero(BindSnd.header.szBodySize, 10);

	sprintf(BindSnd.szMID, id);		//id
	sprintf(BindSnd.szSID, aid);	//agent id
	sprintf(BindSnd.szPWD, pw);		//passwd

	SetFillUpSpace(BindSnd.szMID, 16);
	SetFillUpSpace(BindSnd.szSID, 16);
	SetFillUpSpace(BindSnd.szPWD, 32);

	memcpy(pBuff, (char*)&BindSnd, sizeof(AUTH_BIND_REQ));

	return sizeof(AUTH_BIND_REQ);
}

int CPacketCtrlSKB::getMsg_BIND_REQ(char* pBuff, char* id, char* aid, char* pw, unsigned long tid)
{
	BIND_REQ BindSnd;
	memset((char*)&BindSnd, 0x00, sizeof(BIND_REQ));

	sprintf(BindSnd.header.szJobCode, "A2");
	sprintf(BindSnd.header.szInvoketype, "00");
	sprintf(BindSnd.header.szVersion, "02.10.00");
	sprintf(BindSnd.header.szClientType, "06");
	sprintf(BindSnd.header.szClientVersion, "02.00.00");
	sprintf(BindSnd.header.szTID, "%lu", tid);
	sprintf(BindSnd.header.szBodySize, "64");

	SetFillUpSpace(BindSnd.header.szJobCode, 2);
	SetFillUpSpace(BindSnd.header.szInvoketype, 2);
	SetFillUpSpace(BindSnd.header.szVersion, 8);
	SetFillUpSpace(BindSnd.header.szClientType, 2);
	SetFillUpSpace(BindSnd.header.szClientVersion, 8);
	SetLeftFillUpZero(BindSnd.header.szTID, 10);
	SetLeftFillUpZero(BindSnd.header.szBodySize, 10);

	sprintf(BindSnd.szMID, id);		//id
	sprintf(BindSnd.szSID, aid);	//agent id
	sprintf(BindSnd.szPWD, pw);		//passwd

	SetFillUpSpace(BindSnd.szMID, 16);
	SetFillUpSpace(BindSnd.szSID, 16);
	SetFillUpSpace(BindSnd.szPWD, 32);

	memcpy(pBuff, (char*)&BindSnd, sizeof(BIND_REQ));

	return sizeof(BIND_REQ);
}

int CPacketCtrlSKB::getMsg_DELIVER_REQ(char* pBuff, char* base64msg, vector<string>& vtSend, vector<string>& vtCtnSend, unsigned long tid, int ctn_cnt)
{
	DELIVER_REQ TrnsReq;
	DELIVER_CONTENT TrnsContent;
	DELIVER_WEB_CONTENT	 TrnsWebContent;
	FILE *fp;

	struct tm *t;
	time_t timer;
	size_t result;
	bool isLMS=false;

	long nSize=0;
	int nTotCnt = 0;
	int nTotalDeliverSize = 0;
	int nCtnSize=0;

	char *strContent = (char*)NULL;
	char strCtnPath[256];
	char strCtnType[16];
	char DstData[MAX_BUFF];

	timer = time(NULL);    // 현재 시각을 초 단위로 얻기
	t = localtime(&timer); // 초 단위의 시간을 분리하여 구조체에 넣기

	vector<string>::iterator itrData;
	itrData = vtSend.begin();

	vector<string>::iterator itrCtnData;
	itrCtnData = vtCtnSend.begin();

	isLMS = (strcmp((char*)string(*(itrData+6)).c_str(), "") == 0) ? false : true;
	//컨텐츠 데이터 작업
	//텍스트 파일 컨텐츠

	nTotCnt = ((isLMS) ? 1 : 0) + ctn_cnt;

	strContent = (char*)malloc(sizeof(char)*nTotCnt*MAX_CONTENT_SIZE+512);
	memset(strContent, 0x00, sizeof(char)*nTotCnt*MAX_CONTENT_SIZE+512);
	memset(TrnsContent.bdata_type, 0x00, sizeof(TrnsContent.bdata_type));
	memset(TrnsContent.bdata_size, 0x00, sizeof(TrnsContent.bdata_size));

	if (isLMS)
	{
		char tmpPath[50];
		memset(tmpPath,0x00,50);
		strcpy(tmpPath, (char*)string(*(itrData+6)).c_str());
		strncpy (tmpPath,"TXT_SSN",7);
		sprintf(strCtnPath, "/data/neomms/CNT/%s",tmpPath);	// TXT_PATH

		//log2(strCtnPath, 0, 0);

		//파일 사이즈 구하기
		fp = fopen(strCtnPath, "rb");
		if (fp == NULL)
		{
			sprintf(tmpLog, "CPacketCtrlSKB::getMsg_DELIVER_REQ() FileOpen ERROR Path : [%s], type : [TXT]", strCtnPath);
			log2(tmpLog, 0, 0);
			goto img_start;
		}
		fseek(fp, 0l, SEEK_END);
		nSize = ftell(fp);
		//

		//데이터 구하기
		rewind(fp);
		TrnsContent.bdata = (char*)malloc(sizeof(char)*nSize+1);
		memset(TrnsContent.bdata, 0x00, sizeof(char)*(int)nSize+1);
		result = fread(TrnsContent.bdata, sizeof(char), nSize, fp);
		TrnsContent.bdata[nSize]='\0';
		fclose(fp);
		if (result != nSize)
		{
			if (strContent) free(strContent);
			if (TrnsContent.bdata) free(TrnsContent.bdata);
			sprintf(tmpLog, "CPacketCtrlSKB::getMsg_DELIVER_REQ() TXT ERROR[reading file: %s]", strCtnPath);
			log2(tmpLog, 0, 0);
		}

		//
		//KTC에서 컬러문자태그를 사용하기때문에 SKB는 컬러문자를 지원하지 않는다.
		//그러므로 SKB는 컬러문자태그를 제거 한다.
		memset(DstData, 0x00, MAX_BUFF);
		GetColorTagRemove(TrnsContent.bdata, DstData);
		memset(TrnsContent.bdata, 0x00, nSize);
		strcpy(TrnsContent.bdata, DstData);
		nSize = strlen(TrnsContent.bdata);
		//

		sprintf(TrnsContent.bdata_type, "text/plain");
		SetFillUpSpace(TrnsContent.bdata_type, 24);
		sprintf(TrnsContent.bdata_size, "%d", nSize);
		SetLeftFillUpZero(TrnsContent.bdata_size, 10);

		memcpy(strContent, TrnsContent.bdata_type, 24);
		memcpy(strContent+24, TrnsContent.bdata_size, 10);
		memcpy(strContent+34, TrnsContent.bdata, nSize);

		nCtnSize = 34+nSize;

		if (TrnsContent.bdata) free(TrnsContent.bdata);
	}
	//
img_start:
	//이미지 파일 컨텐츠
	int ctn_idx=0;
	char logMsg[1024];
	for (int i=((isLMS)?1:0); i<nTotCnt; i++)
	{
		sprintf(strCtnPath, "/data/neomms/CNT/%s", (char*)string(*(itrCtnData+(ctn_idx*2))).c_str());	// 컨텐츠 경로
		sprintf(strCtnType, "%s", (char*)string(*(itrCtnData+(ctn_idx*2+1))).c_str());	//컨텐츠 타입

		ctn_idx++;

		//파일 사이즈 구하기
		fp = fopen(strCtnPath, "rb");
		if (fp == NULL)
		{
			sprintf(tmpLog, "CPacketCtrlSKB::getMsg_DELIVER_REQ() FileOpen ERROR Path : [%s]", strCtnPath);
			log2(tmpLog, 0, 0);
			goto packet_start;
		}
		fseek(fp, 0l, SEEK_END);
		nSize = 0;
		nSize = ftell(fp);
		//

		sprintf(logMsg, "mms_id : [%s], ctn_id : [%s], imgPath : [%s] imgSize : [%ld]", 
			(char*)string(*itrData).c_str(), string(*(itrData + 1)).c_str(), strCtnPath, nSize);
		log2(logMsg, 0, 0);

		//데이터 구하기
		rewind(fp);
		TrnsContent.bdata = (char*)malloc(sizeof(char)*nSize+1);
		memset(TrnsContent.bdata, 0x00, sizeof(char)*nSize+1);
		result = fread(TrnsContent.bdata, sizeof(char), nSize, fp);
		TrnsContent.bdata[nSize]='\0';
		fclose(fp);
		if (result != nSize)
		{
			if (strContent) free(strContent);
			if (TrnsContent.bdata) free(TrnsContent.bdata);
			sprintf(tmpLog, "CPacketCtrlSKB::getMsg_DELIVER_REQ() IMG ERROR reading file: [%s], type : [IMG]", strCtnPath);
			log2(tmpLog, 0, 0);
			return -1;
		}
		//
		memset(TrnsContent.bdata_type, 0x00, 24);
		memset(TrnsContent.bdata_size, 0x00, 10);
		sprintf(TrnsContent.bdata_type, "image/jpg");
		SetFillUpSpace(TrnsContent.bdata_type, 24);
		sprintf(TrnsContent.bdata_size, "%ld", nSize);
		SetLeftFillUpZero(TrnsContent.bdata_size, 10);

		memcpy(strContent+nCtnSize, TrnsContent.bdata_type, 24);
		memcpy(strContent+24+nCtnSize, TrnsContent.bdata_size, 10);
		memcpy(strContent+34+nCtnSize, TrnsContent.bdata, nSize);
		//strncat(strContent, TrnsContent.bdata, nSize);

		if (TrnsContent.bdata)
		{
			free(TrnsContent.bdata);
			TrnsContent.bdata = NULL;
		}

		nCtnSize += 34+nSize;
		isLMS = false;
	}
packet_start:
	strContent[nCtnSize]='\0';

	memset(&TrnsReq, 0x00, sizeof(DELIVER_REQ));
	memset(&TrnsWebContent, 0x00, sizeof(DELIVER_WEB_CONTENT));

	nTotalDeliverSize = sizeof(DELIVER_REQ) + nCtnSize + sizeof(DELIVER_WEB_CONTENT);

	// 패킷 데이터 입력
	sprintf(TrnsReq.header.szJobCode, "D2");
	sprintf(TrnsReq.header.szInvoketype, "00");
	sprintf(TrnsReq.header.szVersion, "02.10.00");
	sprintf(TrnsReq.header.szClientType, "06");
	sprintf(TrnsReq.header.szClientVersion, "02.00.00");
	sprintf(TrnsReq.header.szTID, "%lu", tid);
	sprintf(TrnsReq.header.szBodySize, "%d", nTotalDeliverSize-sizeof(HEADER));

	SetFillUpSpace(TrnsReq.header.szJobCode, 2);
	SetFillUpSpace(TrnsReq.header.szInvoketype, 2);
	SetFillUpSpace(TrnsReq.header.szVersion, 8);
	SetFillUpSpace(TrnsReq.header.szClientType, 2);
	SetFillUpSpace(TrnsReq.header.szClientVersion, 8);
	SetLeftFillUpZero(TrnsReq.header.szTID, 10);
	SetLeftFillUpZero(TrnsReq.header.szBodySize, 10);

	sprintf(TrnsReq.cmp_msg_id, "%s", (char*)string(*itrData).c_str());
	sprintf(TrnsReq.rcv_phn_id, "%s", (char*)string(*(itrData + 3)).c_str());
	//전화번호 하이픈 "-", 공백 " " 제거
	SetReplaceTel(TrnsReq.rcv_phn_id, 24);
	sprintf(TrnsReq.odr_fg, "1");
	sprintf(TrnsReq.sms_gb, "1");
	sprintf(TrnsReq.used_cd, (isLMS) ? "10" : "20");
	sprintf(TrnsReq.reserved_fg, "I");
	sprintf(TrnsReq.reserved_dttm, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	sprintf(TrnsReq.cmp_snd_dttm, "%04d%02d%02d%02d%02d%02d", t->tm_year + 1900, t->tm_mon + 1, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);
	sprintf(TrnsReq.saved_fg, "0");
	sprintf(TrnsReq.snd_phn_id, "%s", (char*)string(*(itrData + 2)).c_str());
	//전화번호 하이픈 "-", 공백 " " 제거
	SetReplaceTel(TrnsReq.snd_phn_id, 24);
	sprintf(TrnsReq.NAT_CD, "");
	sprintf(TrnsReq.assign_cd, "00");
	sprintf(TrnsReq.sender_id, "");
	sprintf(TrnsReq.sender_value, "");
	sprintf(TrnsReq.msg, "%s", base64msg);
	sprintf(TrnsReq.callback_url, "");
	sprintf(TrnsReq.bdata_count, "%d", nTotCnt);

	SetLeftFillUpZero(TrnsReq.cmp_msg_id, 20);
	SetFillUpSpace(TrnsReq.rcv_phn_id, 24);
	SetLeftFillUpZero(TrnsReq.odr_fg, 1);
	SetLeftFillUpZero(TrnsReq.sms_gb, 1);
	SetLeftFillUpZero(TrnsReq.used_cd, 2);
	SetFillUpSpace(TrnsReq.reserved_fg, 1);
	SetFillUpSpace(TrnsReq.reserved_dttm, 14);
	SetFillUpSpace(TrnsReq.cmp_snd_dttm, 14);
	SetLeftFillUpZero(TrnsReq.saved_fg, 1);
	SetFillUpSpace(TrnsReq.snd_phn_id, 24);
	SetFillUpSpace(TrnsReq.NAT_CD, 8);
	SetFillUpSpace(TrnsReq.assign_cd, 5);
	SetFillUpSpace(TrnsReq.sender_id, 20);
	SetFillUpSpace(TrnsReq.sender_value, 32);
	SetFillUpSpace(TrnsReq.msg, 200);
	SetFillUpSpace(TrnsReq.callback_url, 200);
	SetLeftFillUpZero(TrnsReq.bdata_count, 2);

	sprintf(TrnsWebContent.cp_id, "");
	sprintf(TrnsWebContent.cp_content_type, "");
	sprintf(TrnsWebContent.content_id, "");
	sprintf(TrnsWebContent.content_price, "");

	SetLeftFillUpZero(TrnsWebContent.cp_id, 16);
	SetLeftFillUpZero(TrnsWebContent.cp_content_type, 4);
	SetLeftFillUpZero(TrnsWebContent.content_id, 16);
	SetLeftFillUpZero(TrnsWebContent.content_price, 8);

	//최종 버퍼에 입력
	memcpy(pBuff, (char*)&TrnsReq, sizeof(DELIVER_REQ));
	memcpy(pBuff+sizeof(DELIVER_REQ), strContent, nCtnSize);
	memcpy(pBuff+sizeof(DELIVER_REQ)+nCtnSize, (char*)&TrnsWebContent, sizeof(DELIVER_WEB_CONTENT));

	pBuff[nTotalDeliverSize]='\0';

/*  로그
	prt(pBuff, "TrnsReq.header.szJobCode", 0, 2+1);
	prt(pBuff, "TrnsReq.header.szInvoketype", 2, 2+1);
	prt(pBuff, "TrnsReq.header.szVersion", 4, 8+1);
	prt(pBuff, "TrnsReq.header.szClientType", 12, 2+1);
	prt(pBuff, "TrnsReq.header.szClientVersion", 14, 8+1);
	prt(pBuff, "TrnsReq.header.szTID", 22, 10+1);
	prt(pBuff, "TrnsReq.header.szBodySize", 32, 10+1);
	prt(pBuff, "TrnsReq.cmp_msg_id", 42, 20+1);
	prt(pBuff, "TrnsReq.rcv_phn_id", 62, 24+1);
	prt(pBuff, "TrnsReq.odr_fg", 86, 1+1);
	prt(pBuff, "TrnsReq.sms_gb", 87, 1+1);
	prt(pBuff, "TrnsReq.used_cd", 88, 2+1);
	prt(pBuff, "TrnsReq.reserved_fg", 90, 1+1);
	prt(pBuff, "TrnsReq.reserved_dttm", 91, 14+1);
	prt(pBuff, "TrnsReq.cmp_snd_dttm", 105, 14+1);
	prt(pBuff, "TrnsReq.saved_fg", 119, 1+1);
	prt(pBuff, "TrnsReq.snd_phn_id", 120, 24+1);
	prt(pBuff, "TrnsReq.NAT_CD", 144, 8+1);
	prt(pBuff, "TrnsReq.assign_cd", 152, 5+1);
	prt(pBuff, "TrnsReq.sender_id", 157, 20+1);
	prt(pBuff, "TrnsReq.sender_value", 177, 32+1);
	prt(pBuff, "TrnsReq.msg", 209, 200+1);
	prt(pBuff, "TrnsReq.callback_url", 409, 200+1);
	prt(pBuff, "TrnsReq.bdata_count", 609, 2+1);
	prt(pBuff, "TrnsReq.bdata_type", 611, 24+1);
	prt(pBuff, "TrnsReq.bdata_size", 635, 10+1);
//	prt(pBuff, "TrnsReq.bdata", 645, 555+1);

//	prt(strContent, "bdata_type", 0, 24+1);
//	prt(strContent, "bdata_size", 24, 10+1);
//	prt(strContent, "bdata_type", 34, 13533+1);
//	prt(strContent, "bdata_type", 13567, 24+1);
//	prt(strContent, "bdata_type", 13591, 10+1);
//	prt(strContent, "bdata_type", 13601, 19403+1);
//*/

	if (strContent) free(strContent);

	return nTotalDeliverSize;
}

void CPacketCtrlSKB::GetColorTagRemove(char *SrcData, char *DstData)
{
	int nLen = 0;
	int i = 0, j = 0;
	char str[MAX_BUFF];
	memset(str, 0x00, MAX_BUFF);
	strcpy(str, SrcData);
	nLen = strlen(str);

	if (strstr(str, "<#Y>") || strstr(str, "<#R>") || strstr(str, "<#G>") || strstr(str, "<#B>"))
	{
		for (i=0,j=0; i<nLen;)
		{
			if (str[i] == '<' && str[i+1] == '#' && str[i+2] == 'Y' && str[i+3] == '>')
			{
				i += 4;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '#' && str[i+2] == 'B' && str[i+3] == '>')
			{
				i += 4;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '#' && str[i+2] == 'R' && str[i+3] == '>')
			{
				i += 4;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '#' && str[i+2] == 'G' && str[i+3] == '>')
			{
				i += 4;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '#' && str[i+2] == 'Y' && str[i+3] == '>')
			{
				i += 4;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '/' && str[i+2] == '#' && str[i+3] == 'Y' && str[i+4] == '>')
			{
				i += 5;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '/' && str[i+2] == '#' && str[i+3] == 'B' && str[i+4] == '>')
			{
				i += 5;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '/' && str[i+2] == '#' && str[i+3] == 'R' && str[i+4] == '>')
			{
				i += 5;
				continue;
			}
			if (str[i] == '<' && str[i+1] == '/' && str[i+2] == '#' && str[i+3] == 'G' && str[i+4] == '>')
			{
				i += 5;
				continue;
			}
			DstData[j] = str[i];
			i++;
			j++;
		}
	}
	else
	{
		strcpy(DstData, str);
	}
}

void prt(char *log, char *name, int idx, int size)
{
	char tmpLog[4096];
	char Log[4096];
	snprintf(tmpLog, size, "%s", &(*(log+idx)));
	sprintf(Log, "%s : [%s]", name, tmpLog);

	log2(Log, 0, 0);
}

int CPacketCtrlSKB::getMsg_PING_REQ(char* pBuff, unsigned long tid)
{
	LINK_CHK PingSnd;
	memset(&PingSnd, 0x00, sizeof(LINK_CHK));

	sprintf(PingSnd.header.szJobCode, "P2");
	sprintf(PingSnd.header.szInvoketype, "00");
	sprintf(PingSnd.header.szVersion, "02.10.00");
	sprintf(PingSnd.header.szClientType, "06");
	sprintf(PingSnd.header.szClientVersion, "02.00.00");
	sprintf(PingSnd.header.szTID, "%lu", tid);
	sprintf(PingSnd.header.szBodySize, "0");

	SetFillUpSpace(PingSnd.header.szJobCode, 2);
	SetFillUpSpace(PingSnd.header.szInvoketype, 2);
	SetFillUpSpace(PingSnd.header.szVersion, 8);
	SetFillUpSpace(PingSnd.header.szClientType, 2);
	SetFillUpSpace(PingSnd.header.szClientVersion, 8);
	SetLeftFillUpZero(PingSnd.header.szTID, 10);
	SetLeftFillUpZero(PingSnd.header.szBodySize, 10);

	memcpy(pBuff, (char*)&PingSnd, sizeof(LINK_CHK));

	return sizeof(LINK_CHK);
}

int CPacketCtrlSKB::getMsg_PONG_RES(char* pBuff, unsigned long tid)
{
	LINK_CHK_ACK PingAck;
	memset(&PingAck, 0x00, sizeof(LINK_CHK_ACK));

	sprintf(PingAck.header.szJobCode, "P2");
	sprintf(PingAck.header.szInvoketype, "01");
	sprintf(PingAck.header.szVersion, "02.10.00");
	sprintf(PingAck.header.szClientType, "06");
	sprintf(PingAck.header.szClientVersion, "02.00.00");
	sprintf(PingAck.header.szTID, "%lu", tid);
	sprintf(PingAck.header.szBodySize, "36");

	SetFillUpSpace(PingAck.header.szJobCode, 2);
	SetFillUpSpace(PingAck.header.szInvoketype, 2);
	SetFillUpSpace(PingAck.header.szVersion, 8);
	SetFillUpSpace(PingAck.header.szClientType, 2);
	SetFillUpSpace(PingAck.header.szClientVersion, 8);
	SetLeftFillUpZero(PingAck.header.szTID, 10);
	SetLeftFillUpZero(PingAck.header.szBodySize, 10);

	sprintf(PingAck.ResultCode, "1000");
	sprintf(PingAck.ResultMessage, "");

	SetFillUpSpace(PingAck.ResultCode, 4);
	SetFillUpSpace(PingAck.ResultMessage, 32);

	memcpy(pBuff, (char*)&PingAck, sizeof(LINK_CHK_ACK));
	return sizeof(LINK_CHK_ACK);
}

int CPacketCtrlSKB::getMsg_REPORT_ACK(char* pBuff, char* cmp_msg_id, unsigned long tid)
{
	REPORT_ACK TrnsRepAck;
	memset(&TrnsRepAck, 0x00, sizeof(REPORT_ACK));

	sprintf(TrnsRepAck.header.szJobCode, "O3");
	sprintf(TrnsRepAck.header.szInvoketype, "01");
	sprintf(TrnsRepAck.header.szVersion, "02.10.00");
	sprintf(TrnsRepAck.header.szClientType, "06");
	sprintf(TrnsRepAck.header.szClientVersion, "02.00.00");
	sprintf(TrnsRepAck.header.szTID, "%lu", tid);
	sprintf(TrnsRepAck.header.szBodySize, "56");

	SetFillUpSpace(TrnsRepAck.header.szJobCode, 2);
	SetFillUpSpace(TrnsRepAck.header.szInvoketype, 2);
	SetFillUpSpace(TrnsRepAck.header.szVersion, 8);
	SetFillUpSpace(TrnsRepAck.header.szClientType, 2);
	SetFillUpSpace(TrnsRepAck.header.szClientVersion, 8);
	SetLeftFillUpZero(TrnsRepAck.header.szTID, 10);
	SetLeftFillUpZero(TrnsRepAck.header.szBodySize, 10);

	sprintf(TrnsRepAck.ResultCode, "1000");
	sprintf(TrnsRepAck.ResultMessage, "");
	sprintf(TrnsRepAck.cmp_msg_id, cmp_msg_id);

	SetFillUpSpace(TrnsRepAck.ResultCode, 4);
	SetFillUpSpace(TrnsRepAck.ResultMessage, 32);
	SetFillUpSpace(TrnsRepAck.cmp_msg_id, 20);

	memcpy(pBuff, (char*)&TrnsRepAck, sizeof(REPORT_ACK));

	return sizeof(REPORT_ACK);
}

int CPacketCtrlSKB::getMsg_CONTROL_ACK(char* pBuff, unsigned long tid)
{
	CONTROL_ACK ControlAck;
	memset(&ControlAck, 0x00, sizeof(CONTROL_ACK));

	sprintf(ControlAck.header.szJobCode, "C2");
	sprintf(ControlAck.header.szInvoketype, "01");
	sprintf(ControlAck.header.szVersion, "02.10.00");
	sprintf(ControlAck.header.szClientType, "06");
	sprintf(ControlAck.header.szClientVersion, "02.00.00");
	sprintf(ControlAck.header.szTID, "%lu", tid);
	sprintf(ControlAck.header.szBodySize, "36");

	SetFillUpSpace(ControlAck.header.szJobCode, 2);
	SetFillUpSpace(ControlAck.header.szInvoketype, 2);
	SetFillUpSpace(ControlAck.header.szVersion, 8);
	SetFillUpSpace(ControlAck.header.szClientType, 2);
	SetFillUpSpace(ControlAck.header.szClientVersion, 8);
	SetLeftFillUpZero(ControlAck.header.szTID, 10);
	SetLeftFillUpZero(ControlAck.header.szBodySize, 10);

	sprintf(ControlAck.ResultCode, "1000");
	sprintf(ControlAck.ResultMessage, "");

	SetFillUpSpace(ControlAck.ResultCode, 4);
	SetFillUpSpace(ControlAck.ResultMessage, 32);

	memcpy(pBuff, (char*)&ControlAck, sizeof(CONTROL_ACK));

	return sizeof(CONTROL_ACK);
}

int CPacketCtrlSKB::getData_BndAck(char* pBuff, vector<string>& vtBndAck)
{
	BIND_ACK *pBndAck = (BIND_ACK*)pBuff;
	char szTemp[32];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pBndAck->ResultCode);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 32+1, "%s", pBndAck->ResultMessage);
		vtBndAck.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getData_AuthBndAck(char* pBuff, vector<string>& vtBndAck)
{
	AUTH_BIND_ACK *pBndAck = (AUTH_BIND_ACK*)pBuff;
	char szTemp[32];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pBndAck->ResultCode);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 32+1, "%s", pBndAck->ResultMessage);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pBndAck->Tps);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pBndAck->ServerCount);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 16+1, "%s", pBndAck->submitserverIP1);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 5+1, "%s", pBndAck->submitserverport1);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 16+1, "%s", pBndAck->submitserverIP2);
		vtBndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 5+1, "%s", pBndAck->submitserverport2);
		vtBndAck.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getData_SndAck(char* pBuff, vector<string>& vtSndAck)
{
	DELIVER_ACK *pSndAck = (DELIVER_ACK*)pBuff;
	char szTemp[32];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pSndAck->ResultCode);
		vtSndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 32+1, "%s", pSndAck->ResultMessage);
		vtSndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 20+1, "%s", pSndAck->cmp_msg_id);
		vtSndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pSndAck->snd_phn_id);
		vtSndAck.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pSndAck->rcv_phn_id);
		vtSndAck.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getData_Ack2Report(char* pBuff, vector<string>& vtReport)
{
	DELIVER_ACK *pSndAck = (DELIVER_ACK*)pBuff;
	char szTemp[32];
	time_t clock;
    struct tm *ctm;
	char dt[14+1];

	time(&clock);
	ctm = localtime(&clock);
	sprintf(dt, "%04d%02d%02d%02d%02d%02d",
		ctm->tm_year+1900, ctm->tm_mon+1, ctm->tm_mday,
		ctm->tm_hour, ctm->tm_min, ctm->tm_sec);
		
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 20+1, "%s", pSndAck->cmp_msg_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pSndAck->snd_phn_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pSndAck->rcv_phn_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 14+1, "%s", dt);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 14+1, "%s", dt);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pSndAck->ResultCode);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", "ETC");
		vtReport.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getData_Report(char* pBuff, vector<string>& vtReport)
{
	REPORT *pReport = (REPORT*)pBuff;
	char szTemp[32];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 20+1, "%s", pReport->cmp_msg_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pReport->snd_phn_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 24+1, "%s", pReport->rcv_phn_id);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 14+1, "%s", pReport->reg_snd_dttm);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 14+1, "%s", pReport->reg_rcv_dttm);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pReport->rslt_val);
		vtReport.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pReport->telco_id);
		vtReport.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getData_Control(char* pBuff, vector<string>& vtControl)
{
	CONTROL *pControl = (CONTROL*)pBuff;
	char szTemp[5];
	try {
		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pControl->ControlCommand);
		vtControl.push_back(szTemp);

		memset(szTemp, 0x00, sizeof(szTemp));
		snprintf(szTemp, 4+1, "%s", pControl->ControlValue);
		vtControl.push_back(szTemp);
	}
	catch (...) {
		return -1;
	}
	return 1;
}

int CPacketCtrlSKB::getMsgCode(char* pBuff)
{
	HEADER *head = (HEADER*)pBuff;
	int nType = -1;

	try {
		if (strncmp(head->szJobCode, "S1", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//서버 리스트 요청 REQUEST
			nType = TYPE_AUTH_BIND_REQ;	//2
		if (strncmp(head->szJobCode, "S1", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//서버 리스트 요청 RESPONSE
			nType = TYPE_AUTH_BIND_ACK;	//3
		if (strncmp(head->szJobCode, "A2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//인증 요청 REQUEST
			nType = TYPE_BIND_REQ;		//4
		if (strncmp(head->szJobCode, "A2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//인증 요청 RESPONSE
			nType = TYPE_BIND_ACK;		//5
		if (strncmp(head->szJobCode, "D2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//메시지 전송 REQUEST
			nType = TYPE_DELIVER_REQ;	//6
		if (strncmp(head->szJobCode, "D2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//메시지 전송 RESPONSE
			nType = TYPE_DELIVER_ACK;	//7
		if (strncmp(head->szJobCode, "P2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//Alive 체크 REQUEST
			nType = TYPE_PING;			//8
		if (strncmp(head->szJobCode, "P2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//Alive 체크 RESPONSE
			nType = TYPE_PONG;			//9
		if (strncmp(head->szJobCode, "O3", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//메시지 발송결과 전송 REQUEST
			nType = TYPE_REPORT;		//10
		if (strncmp(head->szJobCode, "O3", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//메시지 발송결과 전송 RESPONSE
			nType = TYPE_REPORT_ACK;	//11
		if (strncmp(head->szJobCode, "C2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "00", sizeof(head->szInvoketype)) == 0)	//제어명령 REQUEST
			nType = TYPE_CONTROL;		//12
		if (strncmp(head->szJobCode, "C2", sizeof(head->szJobCode)) == 0 && strncmp(head->szInvoketype, "01", sizeof(head->szInvoketype)) == 0)	//제어명령 RESPONSE
			nType = TYPE_CONTROL_ACK;	//13
	}
	catch (...) {
		nType = -1;
	}
	return nType;
}

void log2(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "PacketCtrlSKB", 0, "");
	}
}
