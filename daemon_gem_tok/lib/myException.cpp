/*
 * myException.h
 *
 *  Created on: 2009. 9. 2.
 *      Author: Administrator
 */

#include "myException.h"
#include <iostream>
using namespace std;

myException::myException(int errCode, const string& errMsg)
{
	initVars();
	errorCode = errCode;
	if (errMsg[0])
		errorMsg.append(errMsg);
}

void myException::initVars()
{
	errorCode = 0;
	errorMsg = "";
}

void myException::response()
{
	cout << "Exception Occurred::[" << errorCode << "][" << errorMsg << "]" << endl;
	cout.flush();
}
