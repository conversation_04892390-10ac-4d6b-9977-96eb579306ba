PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint
CFLAGS = -g -Wall -DDEBUG=5 -std=gnu++03
CMMSFLAGS = -g -Wall -D_MMS_MODE

SMS_DBSTRING=neomms
SMS_DBID=bcreal
SMS_DBPASS=bcreal

MMS_DBSTRING=neo223
MMS_DBID=neorcs
MMS_DBPASS=kskybDB0955**

ORG_D=${HOME}/daemon_gem_tok
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=${HOME}/command_gem_tok/obj/sms_ctrlsub++.o
EXT_INC=${HOME}/command_gem_tok/inc

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC1 = ${ORACLE_HOME}/rdbms/public

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(ORA_INC) -I$(ORA_INC1) -I/usr/include/curl
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC) -L$(ORA_INC1) 
ORALIB = -lclntsh -lssl -lcrypto
#LIBS = -lcurl -lpthread -lnsl
LIBS = -lcurl -lpthread -l:libnsl.so.1

CRYPTO_LIBS = -lcrypto

#all: getToken_v1 rcs_media_v1 rcs_send_v1 getReport_v1 
all: getToken_v1

getToken_v1 : $(OBJ_D)/getToken_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/getToken_v1
	
rcs_media_v1 : $(OBJ_D)/rcs_media_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/rcs_media_v1

rcs_send_v1 : $(OBJ_D)/rcs_send_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/rcs_send_v1

getReport_v1 : $(OBJ_D)/getReport_v1.o $(OBJ_D)/Properties.o $(OBJ_D)/DatabaseORA_MMS.o $(OBJ_D)/myException.o $(OBJ_D)/Curl.o $(OBJ_D)/alimTalkApi.o $(OBJ_D)/jsoncpp.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^  $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/getReport_v1

$(OBJ_D)/getToken_v1.o: $(SRC_D)/getToken_v1.cpp
	$(RM) -rf $(OBJ_D)/getToken_v1.*
	$(COPY) $(SRC_D)/getToken_v1.cpp $(OBJ_D)/getToken_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/getToken_v1.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/getToken_v1.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/getToken_v1.cpp
	
$(OBJ_D)/rcs_media_v1.o: $(SRC_D)/rcs_media_v1.cpp
	$(RM) -rf $(OBJ_D)/rcs_media_v1.*
	$(COPY) $(SRC_D)/rcs_media_v1.cpp $(OBJ_D)/rcs_media_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/rcs_media_v1.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/rcs_media_v1.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/rcs_media_v1.cpp	
	
$(OBJ_D)/rcs_send_v1.o: $(SRC_D)/rcs_send_v1.cpp
	$(RM) -rf $(OBJ_D)/rcs_send_v1.*
	$(COPY) $(SRC_D)/rcs_send_v1.cpp $(OBJ_D)/rcs_send_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/rcs_send_v1.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/rcs_send_v1.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/rcs_send_v1.cpp		
	
$(OBJ_D)/getReport_v1.o: $(SRC_D)/getReport_v1.cpp
	$(RM) -rf $(OBJ_D)/getReport_v1.*
	$(COPY) $(SRC_D)/getReport_v1.cpp $(OBJ_D)/getReport_v1.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/getReport_v1.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/getReport_v1.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/getReport_v1.cpp		
		
$(OBJ_D)/atalk_send_v3.o: $(SRC_D)/atalk_send_v3.cpp
	$(RM) -rf $(OBJ_D)/atalk_send_v3.*
	$(COPY) $(SRC_D)/atalk_send_v3.cpp $(OBJ_D)/atalk_send_v3.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/atalk_send_v3.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/atalk_send_v3.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/atalk_send_v3.cpp

$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

# Unused object files - commented out
#$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
#	$(RM) -rf $(OBJ_D)/SocketTCP.*
#	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

#$(OBJ_D)/PacketCtrlSKB.o: $(LIB_D)/PacketCtrlSKB.cpp
#	$(RM) -rf $(OBJ_D)/PacketCtrlSKB.*
#	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

#$(OBJ_D)/PacketCtrlSKB_URL.o: $(LIB_D)/PacketCtrlSKB.cpp
#	$(RM) -rf $(OBJ_D)/PacketCtrlSKB_URL.*
#	$(CC) -o $@ $(CURLFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

#$(OBJ_D)/PacketCtrlSKB_MMS.o: $(LIB_D)/PacketCtrlSKB_MMS.cpp
#	$(RM) -rf $(OBJ_D)/PacketCtrlSKB_MMS.*
#	$(CC) -o $@ $(CFLAGS) $(CMMSFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^
	
$(OBJ_D)/aes256_cbc.o: $(LIB_D)/aes256_cbc.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(CRYPTO_LIBS) -c $^		

$(OBJ_D)/base64.o: $(LIB_D)/base64.cpp
	$(RM) -rf $(OBJ_D)/base64.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) mode=oracle dbms=v7 unsafe_null=yes char_map=string iname=$(OBJ_D)/DatabaseORA_MMS.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(MMS_DBID)/$(MMS_DBPASS)@$(MMS_DBSTRING)
	$(CC) $(CFLAGS) $(CMMSFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o $(CMMSFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

# Unused object file - commented out
#$(OBJ_D)/LogManager.o: $(LIB_D)/LogManager.cpp
#	$(RM) -rf $(OBJ_D)/LogManager.*
#	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $(OBJ_D)/Curl.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/alimTalkApi.o: $(LIB_D)/alimTalkApi.cpp
	$(RM) -rf $(OBJ_D)/alimTalkApi.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $(OBJ_D)/jsoncpp.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

#install:
#	mv $(BIN_D)/alimtalk_mms_tmp $(BIN_D)/alimtalk_mms
#	mv $(BIN_D)/alimtalk_bt_mms_tmp $(BIN_D)/alimtalk_bt_mms
#	rm tp*
