#ifndef _KSKYB_SIMPLE_SMS_H_
#define _KSKYB_SIMPLE_SMS_H_
#include <stdio.h>
#include <errno.h>
#include <sys/socket.h>
#include <arpa/inet.h>

// error code
#define KSKYB_SUCC 0
#define KSKYB_CONFIG_FILE_OPEN_ERROR -1
#define KSKYB_CONFIG_IP_NOT_FOUND -2
#define KSKYB_CONFIG_PORT_NOT_FOUND -3
#define KSKYB_CONFIG_ID_NOT_FOUND -4
#define KSKYB_CONFIG_PASSWORD_NOT_FOUND -5
#define KSKYB_SOCKET_WRITE_ERROR -6
#define KSKYB_SOCKET_CONNECT_ERROR -7
#define BUFF_LEN 32


typedef struct _tag_SEND_DATA {
        char loginID    [10+1];        
        char loginP<PERSON>   [10+1];        
        char ptnSN      [16+1];        
        char dstAddr    [11+1];        
        char callBack   [11+1];        
        char Mesg       [80+1];
} SEND_DATA;


typedef struct _tag_GConf {
    char ip[BUFF_LEN];
    char port[BUFF_LEN];
    char id[BUFF_LEN];
    char pass[BUFF_LEN];
} GCONF;


int connSock(char* address, int nPort);
int sendData(int sockfd,
        char* id,
        char* password,
        char* serial,
        char* dstaddr,
        char* callback,
        char* msg);
int recvData(int sockfd,
        char* buff);
int getConfig(char* file, GCONF* pGconf);


#endif


