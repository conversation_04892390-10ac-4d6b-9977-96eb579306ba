#include "simplesms.h"

int connSock(char* address, int nPort)
{
    int sockfd;
    int sendbuff;
    struct sockaddr_in serv_addr;
    struct timeval tv;

    sendbuff = 49152;
    bzero((char*)&serv_addr,sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(address);
    serv_addr.sin_port = htons((short)nPort);

    if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
        return KSKYB_SOCKET_CONNECT_ERROR;
    }

    alarm(20);
    if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
        alarm(0);
        return KSKYB_SOCKET_CONNECT_ERROR;
    }
    alarm(0);


    if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&sendbuff,sizeof(sendbuff))<0 ) {
        printf("SOCK : sndbuf set ERROR");
    }
    if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&sendbuff,sizeof(sendbuff))<0 ) {
        printf("SOCK : rcvbuf set ERROR");
    }
    tv.tv_sec = 30;
    tv.tv_usec = 0;
    if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
        printf("SOCK : sndtimeo ERROR");
    }

    if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
        printf("SOCK : rcvtimeo ERROR");
    }

    return sockfd;
}


int sendData(int sockfd,
        char* id,
        char* password,
        char* serial,
        char* dstaddr,
        char* callback,
        char* msg)
{

    int writeLen;
    SEND_DATA data;
    
    memset(&data,0x00,sizeof(SEND_DATA));
    memcpy(data.loginID,id,sizeof(data.loginID));
    memcpy(data.loginPWD,password,sizeof(data.loginPWD));
    memcpy(data.ptnSN,serial,sizeof(data.ptnSN));
    memcpy(data.dstAddr,dstaddr,sizeof(data.dstAddr));
    memcpy(data.callBack,callback,sizeof(data.callBack));
    memcpy(data.Mesg,msg,sizeof(data.Mesg));

    writeLen = write(sockfd,&data,sizeof(SEND_DATA));
    if( writeLen != sizeof(SEND_DATA) )
        return KSKYB_SOCKET_WRITE_ERROR;

    return writeLen;
}


int recvData(int sockfd,
        char* buff)
{
    return read(sockfd,buff,4);
}


int getConfig(char* file, GCONF* pGconf)
{
    FILE* fp;
    char buff[BUFF_LEN];
    fp = fopen(file,"rt");
    if( fp == NULL )
    {
        printf("config file open error [%s]\n",strerror(errno));
        return KSKYB_CONFIG_FILE_OPEN_ERROR;
    }

    memset(buff,0x00,sizeof(buff));
    memset(pGconf,0x00,sizeof(GCONF));
    while( fgets(buff,sizeof(buff),fp) )
    {
        if( memcmp(buff,"ip=",3) == 0 )
            memcpy(pGconf->ip,buff+3,sizeof(pGconf->ip));
        if( memcmp(buff,"port=",5) == 0 )
            memcpy(pGconf->port,buff+5,sizeof(pGconf->port));
        if( memcmp(buff,"id=",3) == 0 )
            memcpy(pGconf->id,buff+3,sizeof(pGconf->id));
        if( memcmp(buff,"pass=",5) == 0 )
            memcpy(pGconf->pass,buff+5,sizeof(pGconf->pass));
        memset(buff,0x00,sizeof(buff));
    }

    if( strlen(pGconf->ip) == 0 )
        return KSKYB_CONFIG_IP_NOT_FOUND;
    if( strlen(pGconf->port) == 0 )
        return KSKYB_CONFIG_PORT_NOT_FOUND;
    if( strlen(pGconf->id) == 0 )
        return KSKYB_CONFIG_ID_NOT_FOUND;
    if( strlen(pGconf->pass) == 0 )
        return KSKYB_CONFIG_PASSWORD_NOT_FOUND;

    return KSKYB_SUCC;
}



