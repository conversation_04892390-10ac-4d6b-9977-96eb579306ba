#include <iostream>
#include <algorithm>
#include <vector>

using namespace std;

int main()
{
    vector<int> vector1(5);
    vector<int> vector2(2);
    int i;
    for(i=0;i<5;i++)
        vector1[i] = i;

    random_shuffle(vector1.begin(),vector1.end());

    cout << "before push_heap" << endl;
    for(i=0;i<5;i++)
        cout << vector1[i] << endl;


    for(i=2;i<3;i++)
    {
        push_heap(vector1.begin(),vector1.begin()+i);
        /*
        cout << "===========================" << endl;
        cout << vector1[0] << endl;
        cout << vector1[1] << endl;
        cout << vector1[2] << endl;
        cout << vector1[3] << endl;
        cout << vector1[4] << endl;
        push_heap(vector1[i]);
        */

    }

    cout << "after push_heap" << endl;
    for(i=0;i<5;i++)
        cout << vector1[i] << endl;



    for(i=3;i>2;--i)
    {
        pop_heap(vector2.begin(),vector2.begin()+i);
        cout << vector2[0] << endl;
    }
    /*

    for(i=0;i<5;i++)
        cout << vector1[i] << endl;


    random_shuffle(vector1.begin(),vector1.end());

    make_heap(vector1.begin(),vector1.end());
    sort_heap(vector1.begin(),vector1.end());
*/

    for(i=0;i<5;i++)
        cout << vector1[i] << endl;

    return 0;
}

