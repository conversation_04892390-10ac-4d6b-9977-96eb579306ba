#include<iostream>
#include <stdio.h>
#include <time.h>


using namespace std;

void get_timestring(char *fmt, long n, char *s) 
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
    s[strlen(s)] = ' ';
}





int main()
{
    char date[16];
    memset(date,0x00,sizeof(date));

       get_timestring("%04d%02d%02d,%2d:%2d:%2d", time(NULL), date);
       cout << "[" << date << "]" << endl;

    return 0;
}

