#include <sms_db.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sqlca.h>
#include "telco_common.h"

int _closeDB(sql_context ctx)
{
    struct sqlca sqlca;
    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :ctx;
    mPrint("DB : CLOSE OK");
    return 0;                                                                                                     
}                                                                                                                 
int _initDB(sql_context* ctx, char* UserName, char* passwd, char* DbString)
{
    struct sqlca sqlca;
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR     Username[10];
    VARCHAR     Password[10];
    VARCHAR     dbstring[10];
    EXEC SQL END DECLARE SECTION;

    char errMsg[512];
    int cnt=0;
/*    sql_context tmpCtx; */

    strcpy((char*)Username.arr, UserName);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, passwd);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, DbString);
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT ALLOCATE :*ctx;

    EXEC SQL ENABLE THREADS;
    EXEC SQL CONTEXT USE :*ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring;

    if( sqlca.sqlcode != 0 )
    {
	mPrint("DB : ERROR [%d][%.*s]",sqlca.sqlcode,
		sqlca.sqlerrm.sqlerrml,sqlca.sqlerrm.sqlerrmc
		);
	_closeDB(*ctx);
	return -1;
    }

    mPrint("DB : CONNECT OK [%x]",*ctx);
    return 0;
}

int _putRptDB(int telcoId, int msgId, char* resCode,char* resDesc , 
	char* sendDate, sql_context ctx,char* endTelco)
{
    int ot_sqlcode = -2;
    char szCode[4];
    char ot_sqlmsg[1024];
    struct sqlca sqlca;

    memset(szCode,0x00,sizeof szCode);
    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    memcpy(szCode,resCode,2);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_telco_mms_res(                
		ins_msg_id=>:msgId,           
		res_code=>:szCode,           
		res_desc=>:resDesc,
		telco_id=>:telcoId,           
		send_date=>:sendDate,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	ot_sqlmsg[512] = 0x00;
	mPrint("proc_telco_res2 ERROR [%d][%s][%x]msgid[%d]rescode[%s]telcoid[%d]resDesc[%s]",
		ot_sqlcode,
		ot_sqlmsg,
		ctx,
		msgId,
		szCode,
		telcoId,
		resDesc);
	mPrint("res2---> sqlcode[%d] sqlmsg[%.*s]",sqlca.sqlcode,
		sqlca.sqlerrm.sqlerrml,
		sqlca.sqlerrm.sqlerrmc);
	if( ot_sqlcode == -1 ) return 0;
	return -1;
    }

    return 0;
}


int _putMsgRetryDB(int msgId, int telcoId, int priority, sql_context ctx)
{
    /*
    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_set_msgretry(
		ins_msg_id=>:msgId,
		ins_telco_id=>:telcoId,
		ins_priority=>:priority,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	ot_sqlmsg[512] = 0x00;
	mPrint("proc_set_msgretry ERROR msgId[%d][%d][%s]",
		msgId,
		ot_sqlcode,
		ot_sqlmsg);
	return -1;
    }
 
    */

    return 0;
}

int _putAckDB(stShortMsgAck* packData , int telcoid, sql_context ctx)
{
    int msg_id;
    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;
    char ack_code[2+1];
    char ack_desc[8+1];

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);
    memset(ack_code,0x00,sizeof ack_code);
    memset(ack_desc,0x00,sizeof ack_desc);

    memcpy(ack_code,packData->kCode,2);
    memcpy(ack_desc,packData->tCode,4);
    ack_code[9]=0x00;
    ack_desc[9]=0x00;
    msg_id = atoi(packData->key);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_telco_mms_ack(
		ins_msg_id=>:msg_id,
		ack_code=>:ack_code ,
		ack_desc=>:ack_desc ,
		telco_id=>:telcoid ,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC;

    if( ot_sqlcode != 0 )
    {
	ot_sqlmsg[512] = 0x00;
	mPrint("proc_telco_mms_ack ERROR [%x][%d][%s]",
		ctx,
		ot_sqlcode,
		ot_sqlmsg );
	mPrint("---> sqlcode[%d] sqlmsg[%.*s]",sqlca.sqlcode,
		sqlca.sqlerrm.sqlerrml,
		sqlca.sqlerrm.sqlerrmc);

	return -1;
    }
    return 0;

}

int _getLmsQDB(int telco_id, stLongMsg* msgData, sql_context ctx)
{
    int msg_id;   
    char dstaddr[12+1];   
    char callback[12+1];  
    char msgcls[2+1];
    char routing[3+1];
    char msg_name[100];
    char msg_body[2048];  

    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    memset(dstaddr,0x00,sizeof dstaddr);
    memset(callback,0x00,sizeof callback);
    memset(msgcls,0x00,sizeof msgcls);
    memset(routing,0x00,sizeof routing);
    memset(msg_name,0x00,sizeof msg_name);
    memset(msg_body,0x00,sizeof msg_body);

    EXEC SQL CONTEXT USE :ctx;             
    EXEC SQL EXECUTE
	BEGIN         
	proc_get_lmsdata(                      
		msg_id=>:msg_id,               
		dstaddr=>:dstaddr,             
		callback=>:callback,           
		phone_routing=>:routing , 
		msg_cls=>:msgcls , 
		msg_name=>:msg_name,
		msg_data=>:msg_body,   
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC;


    switch(ot_sqlcode) {
	case 1:
	    msgData->msg_id = msg_id;      
	    strcpy(msgData->DstAddr,(char*)_trimCommon(dstaddr,strlen(dstaddr)));
	    strcpy(msgData->CallBack,(char*)_trimCommon(callback,strlen(callback)));
	    strcpy(msgData->MsgCls,(char*)_trimCommon(msgcls,strlen(msgcls)));
	    strcpy(msgData->Routing,(char*)_trimCommon(routing,strlen(routing)));
	    strcpy(msgData->Name,(char*)_trimCommon(msg_name,strlen(msg_name)));
	    strcpy(msgData->Mesg,(char*)_trimCommon(msg_body,strlen(msg_body)));
	    return ot_sqlcode;
	case 0:
	    return ot_sqlcode;
	case -25228:
	    return 0;
	default:
	    ot_sqlmsg[512] = 0x00;
	    mPrint("proc_get_msgdata2 ERROR [%d][%s]",
		    ot_sqlcode,
		    ot_sqlmsg);
	mPrint("msgdata2 ---> sqlcode[%d] sqlmsg[%.*s]",sqlca.sqlcode,
		sqlca.sqlerrm.sqlerrml,
		sqlca.sqlerrm.sqlerrmc);

	    return -1;
    }

    return -1;
}


