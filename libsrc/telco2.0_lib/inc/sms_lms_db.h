#ifndef _SMS_LMS_DB_H
#define _SMS_LMS_DB_H


#define MAX_BLOB_BUFLEN 300000

typedef struct tag_eachItemInfo {
    char type[2];
    char name[20];
    char id[2];
    char size[10];
} stEachItemInfo;


typedef struct tag_eachItemInfo2 {
    char type[2];
    char name[20];
    char id[2];
    char size[10];
} stEachItemInfo2;

typedef struct tag_eachItemInfo3 {
    char mine[50+1];
    char name[50+1];
} stEachItemInfo3;

#endif
