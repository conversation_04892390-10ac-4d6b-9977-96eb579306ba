#ifndef _SMS_CONFIG_H__
#define _SMS_CONFIG_H__

#define qfValue       qsValue 
#define qfFree        qsFree
#define qfiValue      qsiValue
#define FetchEntry(QENTRY,NAME)        (qfValue(QENTRY, "%s", NAME) != NULL) ? qfValue(QENTRY, "%s", NAME) : ""
#define FetchEntryInt(QENTRY,NAME)     qfiValue(QENTRY, "%s", NAME)


typedef struct Q_Entry Q_Entry; 
struct Q_Entry{
    char *name;
    char *value;
    struct Q_Entry *next;
};

char *qReadFile(char *filename, int *size);
Q_Entry *qsDecoder(char *str);
Q_Entry *qfDecoder(char *filename);
FILE *qfopen(char *path, char *mode);
int _flockopen(FILE *fp);
char *qRemoveSpace(char *str);
char *_makeword(char *str, char stop);
char *qsValue(Q_Entry *first, char *format, ...);
char *_EntryValue(Q_Entry *first, char *name);
void qsFree(Q_Entry *first);
void _EntryFree(Q_Entry *first);
int qAwkStr(char array[][256], char *str, char delim);
int qsiValue(Q_Entry *first, char *format, ...);
int _EntryiValue(Q_Entry *first, char *name);
int info();
char *strncpy2(char *pszDst, char *pszSrc, int nMax);

#endif 
/* usage 
 

#include "sms_config.h"

Q_Entry *pEntry;
userDefineStruct pConf;

if(  (pEntry = qfDecoder(configFilePath) ) == NULL )
{
    printf("fail");
    exit(1);
}

strncpy2(pConf.logLogdir, FetchEntry(pEntry,"log.logdir"), 256);
pConf.dbgetmaxcount = FetchEntryInt(pEntry,"db.getmaxcount");

qfFree(pEntry);
 

*/

