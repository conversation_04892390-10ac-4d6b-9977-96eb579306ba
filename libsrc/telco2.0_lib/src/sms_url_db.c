#include <sms_db.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sqlca.h>
#include "telco_common.h"

int commonInfo()
{
    printf("kskyb sms_db library test\n");
    return 0;
}

int _closeDB(sql_context ctx)
{
    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL COMMIT WORK RELEASE;
    EXEC SQL CONTEXT FREE :ctx;
    mPrint("DB : CLOSE OK");
    return 0;                                                                                                     
}                                                                                                                 


int _initDB(sql_context* ctx, char* UserName, char* passwd, char* DbString)
{
    struct sqlca sqlca;
    EXEC SQL BEGIN DECLARE SECTION;
    VARCHAR     Username[10];
    VARCHAR     Password[10];
    VARCHAR     dbstring[10];
    EXEC SQL END DECLARE SECTION;

    char errMsg[512];
    int cnt=0;
/*    sql_context tmpCtx; */

    strcpy((char*)Username.arr, UserName);
    Username.len = strlen((char*)Username.arr);
    strcpy((char*)Password.arr, passwd);
    Password.len = strlen((char*)Password.arr);
    strcpy((char*)dbstring.arr, DbString);
    dbstring.len = strlen((char*)dbstring.arr);

    EXEC SQL CONTEXT ALLOCATE :*ctx;

    EXEC SQL ENABLE THREADS;
    EXEC SQL CONTEXT USE :*ctx;
    EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring;

    if( sqlca.sqlcode != 0 )
    {
	mPrint("DB : ERROR [%d][%.*s]",sqlca.sqlcode,
		sqlca.sqlerrm.sqlerrml,sqlca.sqlerrm.sqlerrmc
		);
	_closeDB(*ctx);
	return -1;
    }

    mPrint("DB : CONNECT OK [%x]",*ctx);
    return 0;
}

int _putRptDB(int telcoId, int msgId, char* resCode, sql_context ctx,char* endTelco)
{
    int ot_sqlcode = -1;
    char szCode[4];
    char ot_sqlmsg[512];

    memset(szCode,0x00,sizeof szCode);
    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    memcpy(szCode,resCode,2);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_telco_res2(                
		ins_msg_id=>:msgId,           
		res_code=>:szCode,           
		telco_id=>:telcoId,           
		v_end_telco=>:endTelco,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	mPrint("proc_telco_res2 ERROR [%d][%s][%x]msgid[%d]rescode[%s]telcoid[%d]endtelco[%s]",
		ot_sqlcode,
		ot_sqlmsg,
		ctx,
		msgId,
		szCode,
		telcoId,
		endTelco);
	return -1;
    }

    return 0;
}

int _putSKTRptDB(char* skId,char* szCode,int* msg_id,sql_context ctx,char* endTelco) 
{

    int tmpKey;
    int ot_sqlcode = -1;
    char ot_sqlmsg[512];
    struct sqlca sqlca;

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_skt_res(
		in_skt_id=>:skId,
		res_code=>:szCode,
		v_end_telco=>:endTelco,
		out_msg_id=>:tmpKey,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	mPrint("proc_skt_res ERROR [%d][%s]",
		ot_sqlcode,
		ot_sqlmsg);
	(int)(*msg_id) = -1;
	return -1;
    }

    (int)(*msg_id) = tmpKey;

    return 0;
}


int _getSKTMsgIdDB(char* skId, sql_context ctx)
{
    int msgId;
/*    char szSkId[20+1];
    memset(szSkId,0x00,sizeof szSkId);
    strcpy(szSkId,skId);
*/
    int ot_sqlcode = -1;
    char ot_sqlmsg[512];
    char szSKID[16];

    memset(szSKID,0x00,sizeof szSKID);
    strcpy(szSKID,skId);
    

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_get_skid2msgid(
		ins_skt_id=>:szSKID,           
		out_msg_id=>:msgId,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	mPrint("proc_get_skid2msgid ERROR SKTID[%s][%d][%s] ctx[%x]",
		szSKID,
		ot_sqlcode,
		ot_sqlmsg,
		ctx);
	return -1;
    }
    
    return msgId;
}

int _putMsgRetryDB(int msgId, int telcoId, int priority, sql_context ctx)
{
    int ot_sqlcode = -1;
    char ot_sqlmsg[512];

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    EXEC SQL CONTEXT USE :ctx; 
    EXEC SQL EXECUTE
	BEGIN
	proc_set_urlretry(
		ins_msg_id=>:msgId,
		ins_telco_id=>:telcoId,
		ins_priority=>:priority,
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC; 

    if( ot_sqlcode != 0 )
    {
	mPrint("proc_set_urlretry ERROR msgId[%d][%d][%s]",
		msgId,
		ot_sqlcode,
		ot_sqlmsg);
	return -1;
    }
 

    return 0;
}


int _getMsgQDB(int telco_id, stShortMsg* msgData, sql_context ctx)
{
    int msg_id;   
    char dstaddr[12+1];   
    char callback[12+1];  
    char msg_body[86+1];  
    int ot_sqlcode = -1;
    char ot_sqlmsg[512];

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);

    memset(dstaddr,0x00,sizeof dstaddr);
    memset(callback,0x00,sizeof callback);
    memset(msg_body,0x00,sizeof msg_body);
    /* proc_get_urldata2 */

    EXEC SQL CONTEXT USE :ctx;             
    EXEC SQL EXECUTE
	BEGIN         
	proc_get_urldata2(                      
		telco_id=>:telco_id,           
		msg_id=>:msg_id,               
		dstaddr=>:dstaddr,             
		callback=>:callback,           
		msg_body=>:msg_body,   
		ot_sqlcode=>:ot_sqlcode,
		ot_sqlmsg=>:ot_sqlmsg
		);
    END;
    END-EXEC;


    switch(ot_sqlcode) {
	case 1:
	    msgData->msg_id = msg_id;      
	    strcpy(msgData->DstAddr,(char*)_trimCommon(dstaddr,strlen(dstaddr)));
	    strcpy(msgData->CallBack,(char*)_trimCommon(callback,strlen(callback)));
	    strcpy(msgData->Mesg,(char*)_trimCommon(msg_body,strlen(msg_body)));
	    return ot_sqlcode;
	case 0:
	    return ot_sqlcode;
	case -25228:
	    return 0;
	default:
	    mPrint("proc_get_urldata2 ERROR [%d][%s]",
		    ot_sqlcode,
		    ot_sqlmsg);
	    return -1;
    }

    return -1;
}


