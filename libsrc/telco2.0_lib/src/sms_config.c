#include <stdio.h>
#include <sys/file.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>
#include "sms_config.h"

int info()
{
    printf("kskyb sms_config library test\n");
    return 0;
}

char *strncpy2(char *pszDst, char *pszSrc, int nMax) { 
    strncpy(pszDst, pszSrc, nMax);
    if(strlen(pszSrc) >= nMax) pszDst[nMax] = '\0';
    return pszDst;
} 


Q_Entry *qfDecoder(char *filename) {
    Q_Entry *first;
    char *sp;

    if((sp = qReadFile(filename, NULL)) == NULL) return NULL;
    first = qsDecoder(sp);
    free(sp);

    return first;
}



char *qReadFile(char *filename, int *size) {
    FILE *fp;
    struct stat fstat;
    char *sp, *tmp;
    int c, i;

    if(size != NULL) *size = 0;
    if(stat(filename, &fstat) < 0) return NULL;
    if((fp = qfopen(filename, "rb")) == NULL) return NULL;

    sp = (char *)malloc(fstat.st_size + 1);
    for(tmp = sp, i = 0; (c = fgetc(fp)) != EOF; tmp++, i++) *tmp = (char)c;
    *tmp = '\0';

    if(fstat.st_size != i) printf("qReadFile: Size(File:%d, Readed:%s) mismatch.", fstat.st_size, i);
    fclose(fp);
    if(size != NULL) *size = i;
    return sp;
}







Q_Entry *qsDecoder(char *str) {
    Q_Entry *first, *entries, *back;
    char  *org, *buf, *offset;
    int  eos;

    if(str == NULL) return NULL;

    first = entries = back = NULL;

    if((org = strdup(str)) == NULL) printf("qsDecoder(): Memory allocation fail.");

    for(buf = offset = org, eos = 0; eos == 0; ) {
	for(buf = offset; *offset != '\n' && *offset != '\0'; offset++);
	if(*offset == '\0') eos = 1;
	else *offset = '\0', offset++;

	qRemoveSpace(buf);
	if((buf[0] == '#') || (buf[0] == '\0')) continue;

	back = entries;
	entries = (Q_Entry *)malloc(sizeof(Q_Entry));
	if(back != NULL) back->next = entries;
	if(first == NULL) first = entries;

	entries->value = strdup(buf);
	entries->name  = _makeword(entries->value, '=');
	entries->next  = NULL;

	qRemoveSpace(entries->name);
	qRemoveSpace(entries->value);
    }

    free(org);

    return first;
}


FILE *qfopen(char *path, char *mode) {
    FILE *stream;

    if((stream = fopen(path, mode)) == NULL) return NULL;
    _flockopen(stream);
    return stream;
}






int _flockopen(FILE *fp) {
#ifdef _WIN32
    return 0;
#else
#ifdef HAVE_FLOCK
    return flock(fileno(fp), LOCK_EX);
#else
    return 0;
#endif
#endif
}


char *qRemoveSpace(char *str) {
    int i, j;

    if(!str)return NULL;

    for(j = 0; str[j] == ' ' || str[j] == 9 || str[j] == '\r' || str[j] == '\n'; j++);
    for(i = 0; str[j] != '\0'; i++, j++) str[i] = str[j];
    for(i--; (i >= 0) && (str[i] == ' ' || str[i] == 9 || str[i] == '\r' || str[i] == '\n'); i--);
    str[i+1] = '\0';

    return str;
}

char *_makeword(char *str, char stop) {
    char *word;
    int  len, i;

    for(len = 0; ((str[len] != stop) && (str[len])); len++);
    word = (char *)malloc(sizeof(char) * (len + 1));

    for(i = 0; i < len; i++)word[i] = str[i];
    word[i] = '\0';

    if(str[len])len++;
    for(i = len; str[i]; i++)str[i - len] = str[i];
    str[i - len] = '\0';

    return (word);
}

char *qsValue(Q_Entry *first, char *format, ...) {
    char name[1024];
    int status;
    va_list arglist;

    va_start(arglist, format);
    status = vsprintf(name, format, arglist);
    if(strlen(name) + 1 > sizeof(name) || status == EOF) printf("qsValue(): Message is too long or invalid.");
    va_end(arglist);

    return _EntryValue(first, name);
}



char *_EntryValue(Q_Entry *first, char *name) {
    Q_Entry *entries;

    for(entries = first; entries; entries = entries->next) {
	if(!strcmp(name, entries->name))return (entries->value);
    }
    return NULL;
}



void qsFree(Q_Entry *first) {
    _EntryFree(first);
}

void _EntryFree(Q_Entry *first) {
    Q_Entry *entries;

    for(; first; first = entries) {
	entries = first->next; /* copy next to tmp */
	free(first->name);
	free(first->value);
	free(first);
    }
}



int qAwkStr(char array[][256], char *str, char delim) {
    char *bp1, *bp2;
    int i, exitflag;

    for(i = exitflag = 0, bp1 = bp2 = str; exitflag == 0; i++) {
	for(; *bp2 != delim && *bp2 != '\0'; bp2++);
	if(*bp2 == '\0') exitflag = 1;
	*bp2 = '\0';
	strcpy(array[i], bp1);
	bp1 = ++bp2;
    }

    return i;
}





int qsiValue(Q_Entry *first, char *format, ...) {
    char name[1024];
    int status;
    va_list arglist;

    va_start(arglist, format);
    status = vsprintf(name, format, arglist);
    if(strlen(name) + 1 > sizeof(name) || status == EOF) printf("qsiValue(): Message is too long or invalid.");
    va_end(arglist);

    return _EntryiValue(first, name);
}



int _EntryiValue(Q_Entry *first, char *name) {
    char *str;

    str = _EntryValue(first, name);
    if(str == NULL) return 0;
    return atoi(str);
}













