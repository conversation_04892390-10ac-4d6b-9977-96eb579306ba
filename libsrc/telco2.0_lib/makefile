#include /user/neosms/cfg/telco2.0/db.conf
include $(KSKYBCONFIG)/telco2.0/db_mms.conf
COPY=cp
CC=gcc
RM=rm
LINT=lint
PROC=proc
SQLCHECK=FULL
AR=ar

LIBRARY_HOME=$(KSKYBLIB)
ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_OCI = ${ORACLE_HOME}/rdbms/demo
ORA_OCI2 = ${ORACLE_HOME}/rdbms/public
BIN_D=bin
OBJ_D=obj
LIB_D=lib
INC_D=inc
SRC_D=src


INCLUDE =   -I$(INC_D)

CFLAGS = -D_REENTRANT
CFLAGS = -DDEBUG -g -D_REENTRANT  -DLINUX
CFLAGS = -g -D_REENTRANT  -DLINUX
#CFLAGS = -D_REENTRANT  -DLINUX -O2
#CFLAGS = -DDEBUG -g -D_REENTRANT  -DUNIX

LIBS = -lnsl -lpthread -lrt
#LIBS = -lnsl -lpthread -lsocket -lrt

all:	libsms_common.a libsms_config.a libsms_db.a libsms_url_db.a libsms_socket.a libtelco_common.a libtelco_ssn.a libtelco_skt.a libtelco_ssn_url.a  libtelco_skn.a libtelco_dac.a libtelco_ktf.a 


telco: libtelco_ssn.a libtelco_skt.a libtelco_common.a libtelco_ssn_url.a libtelco_skn.a libtelco_dac.a libtelco_ktf.a 

db: libsms_db.a libsms_url_db.a 

sms_db: libsms_db.a
sms_url_db: libsms_url_db.a
sms_lms_db: libsms_lms_db.a

sms_common: libsms_common.a libsms_config.a libsms_socket.a

telco_ssn: libtelco_ssn.a
telco_skn: libtelco_skn.a
telco_dac: libtelco_dac.a
telco_ktf: libtelco_ktf.a
telco_ssn_lms: libtelco_ssn_lms.a

telco_skt: libtelco_skt.a
	
telco_ssn_url: libtelco_ssn_url.a

telco_common: libtelco_common.a

neosms40: libsms_common.a libsms_config.a libsms_socket.a libsms_lms_db.a libtelco_common.a  libtelco_ssn_lms.a

libsms_common.a: $(OBJ_D)/sms_common.o 
	${AR} rc $(LIB_D)/libsms_common.a $(OBJ_D)/sms_common.o

$(OBJ_D)/sms_common.o: $(SRC_D)/sms_common.c
	$(RM) -rf $(OBJ_D)/sms_common.o $(LIB_D)/sms_common.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_common.o -I${INC_D} -c $(SRC_D)/sms_common.c


libsms_config.a: $(OBJ_D)/sms_config.o 
	${AR} rc $(LIB_D)/libsms_config.a $(OBJ_D)/sms_config.o 

$(OBJ_D)/sms_config.o: $(SRC_D)/sms_config.c
	$(RM) -rf $(OBJ_D)/sms_config.o $(LIB_D)/sms_config.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_config.o -I${INC_D} -c $(SRC_D)/sms_config.c


libsms_db.a: $(OBJ_D)/sms_db.o
	${AR} rc $(LIB_D)/libsms_db.a $(OBJ_D)/sms_db.o

libsms_url_db.a: $(OBJ_D)/sms_url_db.o
	${AR} rc $(LIB_D)/libsms_url_db.a $(OBJ_D)/sms_url_db.o


libsms_lms_db.a: $(OBJ_D)/sms_lms_db.o
	${AR} rc $(LIB_D)/libsms_lms_db.a $(OBJ_D)/sms_lms_db.o




$(OBJ_D)/sms_db.o: $(SRC_D)/sms_db.c
	$(RM) -rf $(OBJ_D)/sms_db.o $(LIB_D)/sms_db.a
	$(COPY) $(SRC_D)/sms_db.c $(OBJ_D)/sms_db.pc
	$(PROC) iname=$(OBJ_D)/sms_db.pc include=$(INC_D) include=$(ORA_INC) include=$(LIBRARY_HOME) CTIMEOUT=10 THREAD=YES SQLCHECK=$(SQLCHECK) userid=$(DBID)/$(DBPASS)@$(DBSID)
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_db.o -I${INC_D} -I${ORA_INC} -I$(LIBRARY_HOME) -c $(OBJ_D)/sms_db.c



$(OBJ_D)/sms_url_db.o: $(SRC_D)/sms_url_db.c
	$(RM) -rf $(OBJ_D)/sms_url_db.o $(LIB_D)/sms_url_db.a
	$(COPY) $(SRC_D)/sms_url_db.c $(OBJ_D)/sms_url_db.pc
	$(PROC) iname=$(OBJ_D)/sms_url_db.pc include=$(INC_D) include=$(ORA_INC) include=$(LIBRARY_HOME) CTIMEOUT=10 THREAD=YES SQLCHECK=$(SQLCHECK) userid=$(DBID)/$(DBPASS)@$(DBSID)
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_url_db.o -I${INC_D} -I${ORA_INC} -I$(LIBRARY_HOME) -c $(OBJ_D)/sms_url_db.c

$(OBJ_D)/sms_lms_db.o: $(SRC_D)/sms_lms_db.c
	$(RM) -rf $(OBJ_D)/sms_lms_db.o $(LIB_D)/sms_lms_db.a
	$(COPY) $(SRC_D)/sms_lms_db.c $(OBJ_D)/sms_lms_db.pc
	$(PROC) iname=$(OBJ_D)/sms_lms_db.pc include=$(INC_D) include=$(ORA_INC) include=$(ORA_OCI) include=$(ORA_OCI2) include=$(LIBRARY_HOME) CTIMEOUT=10 THREAD=YES SQLCHECK=$(SQLCHECK) userid=$(DBID)/$(DBPASS)@$(DBSID)
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_lms_db.o -I${INC_D} -I${ORA_INC} -I${ORA_OCI} -I${ORA_OCI2}  -I$(LIBRARY_HOME) -c $(OBJ_D)/sms_lms_db.c



libsms_socket.a: $(OBJ_D)/sms_socket.o
	${AR} rc $(LIB_D)/libsms_socket.a $(OBJ_D)/sms_socket.o 

$(OBJ_D)/sms_socket.o: $(SRC_D)/sms_socket.c
	$(RM) -rf $(OBJ_D)/sms_socket.o $(LIB_D)/sms_socket.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/sms_socket.o -I${INC_D} -c $(SRC_D)/sms_socket.c


libtelco_common.a: $(OBJ_D)/telco_common.o
	${AR} rc $(LIB_D)/libtelco_common.a $(OBJ_D)/telco_common.o 

$(OBJ_D)/telco_common.o: $(SRC_D)/telco_common.c
	$(RM) -rf $(OBJ_D)/telco_common.o $(LIB_D)/telco_common.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_common.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_common.c


libtelco_ssn.a: $(OBJ_D)/telco_ssn.o
	${AR} rc $(LIB_D)/libtelco_ssn.a $(OBJ_D)/telco_ssn.o

libtelco_skn.a: $(OBJ_D)/telco_skn.o
	${AR} rc $(LIB_D)/libtelco_skn.a $(OBJ_D)/telco_skn.o

libtelco_dac.a: $(OBJ_D)/telco_dac.o
	${AR} rc $(LIB_D)/libtelco_dac.a $(OBJ_D)/telco_dac.o

libtelco_ktf.a: $(OBJ_D)/telco_ktf.o
	${AR} rc $(LIB_D)/libtelco_ktf.a $(OBJ_D)/telco_ktf.o

libtelco_ssn_lms.a: $(OBJ_D)/telco_ssn_lms.o
	${AR} rc $(LIB_D)/libtelco_ssn_lms.a $(OBJ_D)/telco_ssn_lms.o


libtelco_ssn_url.a: $(OBJ_D)/telco_ssn_url.o
	${AR} rc $(LIB_D)/libtelco_ssn_url.a $(OBJ_D)/telco_ssn_url.o

$(OBJ_D)/telco_skn.o: $(SRC_D)/telco_skn.c
	$(RM) -rf $(OBJ_D)/telco_skn.o $(LIB_D)/telco_skn.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_skn.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_skn.c

$(OBJ_D)/telco_dac.o: $(SRC_D)/telco_dac.c
	$(RM) -rf $(OBJ_D)/telco_dac.o $(LIB_D)/telco_dac.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_dac.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_dac.c

$(OBJ_D)/telco_ktf.o: $(SRC_D)/telco_ktf.c
	$(RM) -rf $(OBJ_D)/telco_ktf.o $(LIB_D)/telco_ktf.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_ktf.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_ktf.c

$(OBJ_D)/telco_ssn_lms.o: $(SRC_D)/telco_ssn_lms.c
	$(RM) -rf $(OBJ_D)/telco_ssn_lms.o $(LIB_D)/telco_ssn_lms.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_ssn_lms.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_ssn_lms.c

$(OBJ_D)/telco_ssn.o: $(SRC_D)/telco_ssn.c
	$(RM) -rf $(OBJ_D)/telco_ssn.o $(LIB_D)/telco_ssn.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_ssn.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_ssn.c


$(OBJ_D)/telco_ssn_url.o: $(SRC_D)/telco_ssn_url.c
	$(RM) -rf $(OBJ_D)/telco_ssn_url.o $(LIB_D)/telco_ssn_url.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_ssn_url.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_ssn_url.c


libtelco_skt.a: $(OBJ_D)/telco_skt.o
	${AR} rc $(LIB_D)/libtelco_skt.a $(OBJ_D)/telco_skt.o

$(OBJ_D)/telco_skt.o: $(SRC_D)/telco_skt.c
	$(RM) -rf $(OBJ_D)/telco_skt.o $(LIB_D)/telco_skt.a
	$(CC) -fPIC $(CFLAGS) -o $(OBJ_D)/telco_skt.o -I${INC_D} -I$(LIBRARY_HOME) -c $(SRC_D)/telco_skt.c




install: 
	$(COPY) $(LIB_D)/libsms_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_config.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_config.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_db.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libsms_url_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)


	$(COPY) $(LIB_D)/libsms_socket.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_socket.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_ssn.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn.h $(LIBRARY_HOME)
	
	$(COPY) $(LIB_D)/libtelco_skn.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skn.h $(LIBRARY_HOME)
	
	$(COPY) $(LIB_D)/libtelco_dac.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_dac.h $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ktf.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ktf.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_ssn_url.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_skt.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skt.h $(LIBRARY_HOME)


telco_install:
	$(COPY) $(LIB_D)/libtelco_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_ssn.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_skn.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_dac.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ssn_url.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn.h $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skn.h $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_dac.h $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_skt.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skt.h $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ktf.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ktf.h $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ssn_lms.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn_lms.h $(LIBRARY_HOME)







telco_ssn_install:
	$(COPY) $(LIB_D)/libtelco_ssn_url.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ssn.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn.h $(LIBRARY_HOME)

telco_skn_install:
	$(COPY) $(LIB_D)/libtelco_skn.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skn.h $(LIBRARY_HOME)

telco_dac_install:
	$(COPY) $(LIB_D)/libtelco_dac.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_dac.h $(LIBRARY_HOME)

telco_ktf_install:
	$(COPY) $(LIB_D)/libtelco_ktf.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ktf.h $(LIBRARY_HOME)
telco_ssn_lms_install:
	$(COPY) $(LIB_D)/libtelco_ssn_lms.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn_lms.h $(LIBRARY_HOME)




telco_skt_install:
	$(COPY) $(LIB_D)/libtelco_skt.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_skt.h $(LIBRARY_HOME)



telco_common_install:
	$(COPY) $(LIB_D)/libtelco_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_common.h $(LIBRARY_HOME)

sms_common_install:
	$(COPY) $(LIB_D)/libsms_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_config.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_config.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_socket.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_socket.h $(LIBRARY_HOME)


db_install:
	$(COPY) $(LIB_D)/libsms_db.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libsms_url_db.a $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libsms_lms_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)


sms_db_install:
	$(COPY) $(LIB_D)/libsms_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)

sms_url_db_install:
	$(COPY) $(LIB_D)/libsms_url_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)


sms_lms_db_install:
	$(COPY) $(LIB_D)/libsms_lms_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)


neosms40_install: 
	$(COPY) $(LIB_D)/libsms_lms_db.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_db.h $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_lms_db.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libtelco_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_common.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_common.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_config.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_config.h $(LIBRARY_HOME)

	$(COPY) $(LIB_D)/libsms_socket.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/sms_socket.h $(LIBRARY_HOME)
	$(COPY) $(LIB_D)/libtelco_ssn_lms.a $(LIBRARY_HOME)
	$(COPY) $(INC_D)/telco_ssn_lms.h $(LIBRARY_HOME)


clean :
	$(RM) -rf $(OBJ_D)/*.pc $(OBJ_D)/*.lis $(OBJ_D)/*.c $(OBJ_D)/*.o tp*
