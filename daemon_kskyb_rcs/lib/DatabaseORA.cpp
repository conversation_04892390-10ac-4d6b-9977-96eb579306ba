/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA.h"
#include <stdio.h>
#include <string.h> 
#include <stdlib.h> 
#include <sqlca.h>
#include <iostream>
using namespace std;

namespace KSKYB
{

char *CDatabaseORA::TrimR(char *szOrg, int leng)
{
	int i = 0;

	for(i = leng -1; 1 >= 0; i--) {
		if(isspace(szOrg[i])){
			szOrg[i] = 0x00;
		}
		else {
			break;
		}
	}
	return szOrg;
}


int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::initThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::initThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::freeThread() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[25+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		cout << "CDatabaseORA::connectToOracle() Error[Parameter is NULL]" << endl;
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::connectToOracle() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		cout << "CDatabaseORA::closeFromOracle() Error["<< sqlca.sqlcode <<"]["<< sqlca.sqlerrm.sqlerrmc <<"]" << endl;
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::getSendData(sql_context ctx, int ipart, int tpart, int  iline, vector<SNDSKY>& vtSend)
{
	SNDSKY sw;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int s_tran_type;
	int s_tran_txt_cnt = 0;
	int s_tran_img_cnt = 0;
	int s_tran_replace_img_cnt = 0;
	int i_part, i_tpart, i_line;
	char s_tran_pr      	 [32   +1];
	char s_tran_id      	 [20   +1];
	char s_tran_mbase_id     [40   +1];
	char s_tran_group_id     [20   +1];
	char s_tran_head    	 [1    +1];
	char s_tran_footer       [20   +1];
	char s_tran_phone   	 [15   +1];
	char s_tran_callback   	 [15   +1];
	char s_tran_msg1         [2600 +1];
	char s_tran_msg2         [120 +1];
	char s_tran_msg3         [120 +1];
	char s_tran_msg4         [120 +1];
	char s_tran_msg5         [120 +1];
	char s_tran_msg6         [120 +1];
	char s_tran_button1      [1300 +1];
	char s_tran_button2      [1300 +1];
	char s_tran_button3      [1300 +1];
	char s_tran_button4      [1300 +1];
	char s_tran_button5      [1300 +1];
	char s_tran_button6      [1300 +1];
	char s_tran_button7      [1300 +1];
	char s_tran_button8      [1300 +1];
	char s_tran_button9      [1300 +1];
	char s_tran_button10     [1300 +1];
	char s_tran_button11     [1300 +1];
	char s_tran_button12     [1300 +1];
	char s_ttl             	 [14   +1];
	char s_tran_date       	 [14   +1];
	char s_tran_title1       [60   +1];
	char s_tran_title2       [60   +1];
	char s_tran_title3       [60   +1];
	char s_tran_title4       [60   +1];
	char s_tran_title5       [60   +1];
	char s_tran_title6       [60   +1];
	char s_tran_file_id1     [120  +1];
	char s_tran_file_id2     [120  +1];
	char s_tran_file_id3     [120  +1];
	char s_tran_file_id4     [120  +1];
	char s_tran_file_id5     [120  +1];
	char s_tran_file_id6     [120  +1]; 
	char s_tran_replace_flag [1    +1];
	char s_tran_replace_title[200  +1];
	char s_tran_replace_msg  [2000 +1];
	char s_tran_replcae_ctn1 [255  +1];
	char s_tran_replcae_ctn2 [255  +1];
	char s_tran_replcae_ctn3 [255  +1];
	EXEC SQL END DECLARE SECTION;
	
	i_part  = ipart;
	i_tpart = tpart;
	i_line  = iline;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL DECLARE cursor_1 CURSOR FOR
		SELECT  TRAN_PR          	TRAN_PR,
            NVL(TRAN_ID,' ')        TRAN_ID,
            TRAN_MBASE_ID           TRAN_MBASE_ID,
            NVL(TRAN_GROUP_ID,' ')  TRAN_GROUP_ID,
            TRAN_HEAD        		TRAN_HEAD,
            TRAN_FOOTER      		TRAN_FOOTER,
            TRAN_PHONE       		TRAN_PHONE,
            TRAN_CALLBACK    		TRAN_CALLBACK,
            NVL(TRAN_MSG1,' ')      TRAN_MSG1,
            NVL(TRAN_MSG2,' ')      TRAN_MSG2,
            NVL(TRAN_MSG3,' ')      TRAN_MSG3,
            NVL(TRAN_MSG4,' ')      TRAN_MSG4,
            NVL(TRAN_MSG5,' ')      TRAN_MSG5,
            NVL(TRAN_MSG6,' ')      TRAN_MSG6,
            TRAN_TYPE         		TRAN_TYPE,
            NVL(TRAN_BUTTON1,' ')    TRAN_BUTTON1,
            NVL(TRAN_BUTTON2,' ')    TRAN_BUTTON2,
            NVL(TRAN_BUTTON3,' ')    TRAN_BUTTON3,
            NVL(TRAN_BUTTON4,' ')    TRAN_BUTTON4,
            NVL(TRAN_BUTTON5,' ')    TRAN_BUTTON5,
            NVL(TRAN_BUTTON6,' ')    TRAN_BUTTON6,
            NVL(TRAN_BUTTON7,' ')    TRAN_BUTTON7,
            NVL(TRAN_BUTTON8,' ')    TRAN_BUTTON8,
            NVL(TRAN_BUTTON9,' ')    TRAN_BUTTON9,
            NVL(TRAN_BUTTON10,' ')    TRAN_BUTTON10,
            NVL(TRAN_BUTTON11,' ')    TRAN_BUTTON11,
            NVL(TRAN_BUTTON12,' ')    TRAN_BUTTON12,
            TO_CHAR(TRAN_DATE + 4320/1440 ,'YYYYMMDDHH24MISS') TTL,
            TO_CHAR(TRAN_DATE,'YYYYMMDDHH24MISS') TRAN_DATE,
            NVL(TRAN_TITLE1,' ')           	 TRAN_TITLE1,
            NVL(TRAN_TITLE2,' ')           	 TRAN_TITLE2,
            NVL(TRAN_TITLE3,' ')           	 TRAN_TITLE3,
            NVL(TRAN_TITLE4,' ')           	 TRAN_TITLE4,
            NVL(TRAN_TITLE5,' ')           	 TRAN_TITLE5,
            NVL(TRAN_TITLE6,' ')           	 TRAN_TITLE6,
            NVL(TRAN_FILE_ID1,' ')      	 TRAN_FILE_ID1,
            NVL(TRAN_FILE_ID2,' ')      	 TRAN_FILE_ID2,
            NVL(TRAN_FILE_ID3,' ')      	 TRAN_FILE_ID3,
            NVL(TRAN_FILE_ID4,' ')      	 TRAN_FILE_ID4,
            NVL(TRAN_FILE_ID5,' ')      	 TRAN_FILE_ID5,
            NVL(TRAN_FILE_ID6,' ')      	 TRAN_FILE_ID6,
            NVL(TRAN_REPLACE_FLAG,'N')       TRAN_REPLACE_FLAG,
            NVL(TRAN_REPLACE_TITLE,' ')      TRAN_REPLACE_TITLE,
            NVL(TRAN_REPLACE_MSG,' ')      	 TRAN_REPLACE_MSG,
            NVL(TRAN_REPLCAE_CTN1,' ')       TRAN_REPLCAE_CTN1,
            NVL(TRAN_REPLCAE_CTN2,' ')       TRAN_REPLCAE_CTN2,
            NVL(TRAN_REPLCAE_CTN3,' ')       TRAN_REPLCAE_CTN3
       FROM TBL_RCS_MSG
      WHERE TRAN_STATUS = '1'
        AND TRAN_TYPE = :i_line
        AND TRAN_DATE < SYSDATE
        AND TO_CHAR(TRAN_DATE, 'YYYYMMDDHH24MISS') > TO_CHAR(SYSDATE-4320/1440, 'YYYYMMDDHH24MISS')
        AND ROWNUM < 101
        FOR UPDATE
	;
	EXEC SQL OPEN cursor_1;
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getSendData:CURSOR1 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	for(int i = 0;;i++)
	{
		memset(s_tran_pr      , 0x00, sizeof(s_tran_pr      ));	
	    memset(s_tran_id      , 0x00, sizeof(s_tran_id      ));	
	    memset(s_tran_mbase_id, 0x00, sizeof(s_tran_mbase_id)); 
	    memset(s_tran_group_id, 0x00, sizeof(s_tran_group_id)); 
	    memset(s_tran_head    , 0x00, sizeof(s_tran_head    ));	
	    memset(s_tran_footer  , 0x00, sizeof(s_tran_footer  )); 
	    memset(s_tran_phone   , 0x00, sizeof(s_tran_phone   ));	
	    memset(s_tran_callback, 0x00, sizeof(s_tran_callback)); 
	    memset(s_tran_msg1    , 0x00, sizeof(s_tran_msg1    )); 
	    memset(s_tran_msg2    , 0x00, sizeof(s_tran_msg2    )); 
	    memset(s_tran_msg3    , 0x00, sizeof(s_tran_msg3    )); 
	    memset(s_tran_msg4    , 0x00, sizeof(s_tran_msg4    )); 
	    memset(s_tran_msg5    , 0x00, sizeof(s_tran_msg5    )); 
	    memset(s_tran_msg6    , 0x00, sizeof(s_tran_msg6    )); 
	    memset(s_tran_button1  , 0x00, sizeof(s_tran_button1  )); 
	    memset(s_tran_button2  , 0x00, sizeof(s_tran_button2  )); 
	    memset(s_tran_button3  , 0x00, sizeof(s_tran_button3  )); 
	    memset(s_tran_button4  , 0x00, sizeof(s_tran_button4  )); 
	    memset(s_tran_button5  , 0x00, sizeof(s_tran_button5  )); 
	    memset(s_tran_button6  , 0x00, sizeof(s_tran_button6  )); 
	    memset(s_tran_button7  , 0x00, sizeof(s_tran_button7  )); 
	    memset(s_tran_button8  , 0x00, sizeof(s_tran_button8  )); 
	    memset(s_tran_button9  , 0x00, sizeof(s_tran_button9  )); 
	    memset(s_tran_button10  , 0x00, sizeof(s_tran_button10  )); 
	    memset(s_tran_button11  , 0x00, sizeof(s_tran_button11  )); 
	    memset(s_tran_button12  , 0x00, sizeof(s_tran_button12  )); 
	    memset(s_ttl          , 0x00, sizeof(s_ttl          )); 
	    memset(s_tran_date    , 0x00, sizeof(s_tran_date    )); 
	    memset(s_tran_title1  , 0x00, sizeof(s_tran_title1  )); 
	    memset(s_tran_title2  , 0x00, sizeof(s_tran_title2  )); 
	    memset(s_tran_title3  , 0x00, sizeof(s_tran_title3  )); 
	    memset(s_tran_title4  , 0x00, sizeof(s_tran_title4  )); 
	    memset(s_tran_title5  , 0x00, sizeof(s_tran_title5  )); 
	    memset(s_tran_file_id1, 0x00, sizeof(s_tran_file_id1)); 
	    memset(s_tran_file_id2, 0x00, sizeof(s_tran_file_id2)); 
	    memset(s_tran_file_id3, 0x00, sizeof(s_tran_file_id3)); 
	    memset(s_tran_file_id4, 0x00, sizeof(s_tran_file_id4)); 
	    memset(s_tran_file_id5, 0x00, sizeof(s_tran_file_id5)); 
	    memset(s_tran_file_id6, 0x00, sizeof(s_tran_file_id6)); 
	    memset(s_tran_replace_flag , 0x00, sizeof(s_tran_replace_flag )); 
	    memset(s_tran_replace_title, 0x00, sizeof(s_tran_replace_title)); 
	    memset(s_tran_replace_msg  , 0x00, sizeof(s_tran_replace_msg  )); 
	    memset(s_tran_replcae_ctn1 , 0x00, sizeof(s_tran_replcae_ctn1 )); 
	    memset(s_tran_replcae_ctn2 , 0x00, sizeof(s_tran_replcae_ctn2 )); 
	    memset(s_tran_replcae_ctn3 , 0x00, sizeof(s_tran_replcae_ctn3 ));


		EXEC SQL FETCH cursor_1
			INTO :s_tran_pr      ,
			     :s_tran_id      ,
			     :s_tran_mbase_id,
			     :s_tran_group_id,
			     :s_tran_head    ,
			     :s_tran_footer  ,
			     :s_tran_phone   ,
			     :s_tran_callback,
			     :s_tran_msg1    ,
			     :s_tran_msg2    ,
			     :s_tran_msg3    ,
			     :s_tran_msg4    ,
			     :s_tran_msg5    ,
			     :s_tran_msg6    ,
			     :s_tran_type    ,
			     :s_tran_button1  ,
			     :s_tran_button2  ,
			     :s_tran_button3  ,
			     :s_tran_button4  ,
			     :s_tran_button5  ,
			     :s_tran_button6  ,
			     :s_tran_button7  ,
			     :s_tran_button8  ,
			     :s_tran_button9  ,
			     :s_tran_button10  ,
			     :s_tran_button11  ,
			     :s_tran_button12  ,		
			     :s_ttl          ,
			     :s_tran_date    ,
			     :s_tran_title1  ,
			     :s_tran_title2  ,
			     :s_tran_title3  ,
			     :s_tran_title4  ,
			     :s_tran_title5  ,
			     :s_tran_title6  ,
			     :s_tran_file_id1,
			     :s_tran_file_id2,
			     :s_tran_file_id3,
			     :s_tran_file_id4,
			     :s_tran_file_id5,
			     :s_tran_file_id6,
			     :s_tran_replace_flag,
				 :s_tran_replace_title,
				 :s_tran_replace_msg,  
				 :s_tran_replcae_ctn1, 
				 :s_tran_replcae_ctn2,
				 :s_tran_replcae_ctn3 
	
		;
		
		if(sqlca.sqlcode == 1403)
		{
			EXEC SQL CLOSE cursor_1;
			return 1;
		}
		
		//sprintf(tmpLog3, "shs getSendData:FETCH1 [%s][%d][%.150s]", s_tran_pr,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		//	log3(tmpLog3, 0, 0);
		
		
		trim3(s_tran_pr      , strlen(s_tran_pr      ));	
	    trim3(s_tran_id      , strlen(s_tran_id      ));	
	    trim3(s_tran_mbase_id, strlen(s_tran_mbase_id)); 
	    trim3(s_tran_group_id, strlen(s_tran_group_id)); 
	    trim3(s_tran_head    , strlen(s_tran_head    ));	
	    trim3(s_tran_footer  , strlen(s_tran_footer  )); 
	    trim3(s_tran_phone   , strlen(s_tran_phone   ));	
	    trim3(s_tran_callback, strlen(s_tran_callback)); 
	    trim3(s_tran_msg1    , strlen(s_tran_msg1    )); 
	    trim3(s_tran_msg2    , strlen(s_tran_msg2    )); 
	    trim3(s_tran_msg3    , strlen(s_tran_msg3    )); 
	    trim3(s_tran_msg4    , strlen(s_tran_msg4    )); 
	    trim3(s_tran_msg5    , strlen(s_tran_msg5    )); 
	    trim3(s_tran_msg6    , strlen(s_tran_msg6    )); 
	    trim3(s_tran_button1  , strlen(s_tran_button1  )); 
	    trim3(s_tran_button2  , strlen(s_tran_button2  )); 
	    trim3(s_tran_button3  , strlen(s_tran_button3  )); 
	    trim3(s_tran_button4  , strlen(s_tran_button4  )); 
	    trim3(s_tran_button5  , strlen(s_tran_button5  )); 
	    trim3(s_tran_button6  , strlen(s_tran_button6  )); 
	    trim3(s_tran_button7  , strlen(s_tran_button7  )); 
	    trim3(s_tran_button8  , strlen(s_tran_button8  )); 
	    trim3(s_tran_button9  , strlen(s_tran_button9  )); 
	    trim3(s_tran_button10  , strlen(s_tran_button10  )); 
	    trim3(s_tran_button11  , strlen(s_tran_button11  )); 
	    trim3(s_tran_button12  , strlen(s_tran_button12  )); 
	    trim3(s_ttl          , strlen(s_ttl          )); 
	    trim3(s_tran_date    , strlen(s_tran_date    )); 
	    trim3(s_tran_title1  , strlen(s_tran_title1  )); 
	    trim3(s_tran_title2  , strlen(s_tran_title2  )); 
	    trim3(s_tran_title3  , strlen(s_tran_title3  )); 
	    trim3(s_tran_title4  , strlen(s_tran_title4  )); 
	    trim3(s_tran_title5  , strlen(s_tran_title5  )); 
	    trim3(s_tran_title6  , strlen(s_tran_title6  )); 
	    trim3(s_tran_file_id1, strlen(s_tran_file_id1)); 
	    trim3(s_tran_file_id2, strlen(s_tran_file_id2)); 
	    trim3(s_tran_file_id3, strlen(s_tran_file_id3)); 
	    trim3(s_tran_file_id4, strlen(s_tran_file_id4)); 
	    trim3(s_tran_file_id5, strlen(s_tran_file_id5)); 
	    trim3(s_tran_file_id6, strlen(s_tran_file_id6));
	    trim3(s_tran_replace_flag ,strlen(s_tran_replace_flag ));
		trim3(s_tran_replace_title,strlen(s_tran_replace_title));
		trim3(s_tran_replace_msg  ,strlen(s_tran_replace_msg  ));
		trim3(s_tran_replcae_ctn1 ,strlen(s_tran_replcae_ctn1 ));
		trim3(s_tran_replcae_ctn2 ,strlen(s_tran_replcae_ctn2 ));
		trim3(s_tran_replcae_ctn3 ,strlen(s_tran_replcae_ctn3 ));
 
	    
	    if(strlen(s_tran_msg1) > 0 )
	    	s_tran_txt_cnt +=1;
	    if(strlen(s_tran_msg2) > 0 )
	    	s_tran_txt_cnt +=1;	
	    if(strlen(s_tran_msg3) > 0 )
	    	s_tran_txt_cnt +=1;	
	    if(strlen(s_tran_msg4) > 0 )
	    	s_tran_txt_cnt +=1;		
	    if(strlen(s_tran_msg5) > 0 )
	    	s_tran_txt_cnt +=1;		
	    if(strlen(s_tran_msg6) > 0 )
	    	s_tran_txt_cnt +=1;						
	    	
	    if(strlen(s_tran_file_id1) > 0 )
	    	s_tran_img_cnt +=1;
	    if(strlen(s_tran_file_id2) > 0 )
	    	s_tran_img_cnt +=1;	
	    if(strlen(s_tran_file_id3) > 0 )
	    	s_tran_img_cnt +=1;	
	    if(strlen(s_tran_file_id4) > 0 )
	    	s_tran_img_cnt +=1;		
	    if(strlen(s_tran_file_id5) > 0 )
	    	s_tran_img_cnt +=1;		
	    if(strlen(s_tran_file_id6) > 0 )
	    	s_tran_img_cnt +=1;	
	    	
	    if(strlen(s_tran_replcae_ctn1) > 0 )	
	    	s_tran_replace_img_cnt +=1;	
	    if(strlen(s_tran_replcae_ctn2) > 0 )	
	    	s_tran_replace_img_cnt +=1;	
	    if(strlen(s_tran_replcae_ctn3) > 0 )	
	    	s_tran_replace_img_cnt +=1;				
	    	
	    	
	    //sprintf(tmpLog3, "getSendData:sw insert before::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		//	log3(tmpLog3, 0, 0);

		memset(&sw, 0x00, sizeof(sw));
		
		snprintf(sw.s_tran_pr 		, sizeof(s_tran_pr   	 )	, s_tran_pr  				 );
		snprintf(sw.s_tran_id 		, sizeof(s_tran_id   	 )	, s_tran_id  				 );
		snprintf(sw.s_tran_mbase_id , sizeof(s_tran_mbase_id )	, s_tran_mbase_id  			 );
		snprintf(sw.s_tran_group_id , sizeof(s_tran_group_id )	, s_tran_group_id  			 );
		snprintf(sw.s_tran_head 	, sizeof(s_tran_head   	 )	, s_tran_head  				 );
		snprintf(sw.s_tran_footer 	, sizeof(s_tran_footer   )	, s_tran_footer  			 );
		snprintf(sw.s_tran_phone   	, sizeof(s_tran_phone	 )	, s_tran_phone       		 );
		snprintf(sw.s_tran_callback , sizeof(s_tran_callback )	, s_tran_callback       	 );
		memcpy  (sw.s_tran_msg1     , s_tran_msg1      		  	, sizeof(s_tran_msg1     	));
		memcpy  (sw.s_tran_msg2     , s_tran_msg2      			, sizeof(s_tran_msg2     	));
		memcpy  (sw.s_tran_msg3     , s_tran_msg3      			, sizeof(s_tran_msg3     	));
		memcpy  (sw.s_tran_msg4     , s_tran_msg4      			, sizeof(s_tran_msg4     	));
		memcpy  (sw.s_tran_msg5     , s_tran_msg5      			, sizeof(s_tran_msg5     	));
		memcpy  (sw.s_tran_msg6     , s_tran_msg6      			, sizeof(s_tran_msg6     	));
		memcpy  (sw.s_tran_button1   , s_tran_button1    			, sizeof(s_tran_button1		));
		memcpy  (sw.s_tran_button2   , s_tran_button2    			, sizeof(s_tran_button2		));
		memcpy  (sw.s_tran_button3   , s_tran_button3    			, sizeof(s_tran_button3		));
		memcpy  (sw.s_tran_button4   , s_tran_button4    			, sizeof(s_tran_button4		));
		memcpy  (sw.s_tran_button5   , s_tran_button5    			, sizeof(s_tran_button5		));
		memcpy  (sw.s_tran_button6   , s_tran_button6    			, sizeof(s_tran_button6		));
		memcpy  (sw.s_tran_button7   , s_tran_button7    			, sizeof(s_tran_button7		));
		memcpy  (sw.s_tran_button8   , s_tran_button8    			, sizeof(s_tran_button8		));
		memcpy  (sw.s_tran_button9   , s_tran_button9    			, sizeof(s_tran_button9		));
		memcpy  (sw.s_tran_button10   , s_tran_button10    			, sizeof(s_tran_button10		));
		memcpy  (sw.s_tran_button11   , s_tran_button11    			, sizeof(s_tran_button11		));
		memcpy  (sw.s_tran_button12   , s_tran_button12    			, sizeof(s_tran_button12		));
		snprintf(sw.s_ttl     		, sizeof(s_ttl     	 	 )	, s_ttl      				 );
		snprintf(sw.s_tran_date     , sizeof(s_tran_date 	 )	, s_tran_date      			 );
		memcpy  (sw.s_tran_title1   , s_tran_title1    			, sizeof(s_tran_title1		));
		memcpy  (sw.s_tran_title2   , s_tran_title2    			, sizeof(s_tran_title2		));
		memcpy  (sw.s_tran_title3   , s_tran_title3    			, sizeof(s_tran_title3		));
		memcpy  (sw.s_tran_title4   , s_tran_title4    			, sizeof(s_tran_title4		));
		memcpy  (sw.s_tran_title5   , s_tran_title5    			, sizeof(s_tran_title5		));
		memcpy  (sw.s_tran_title6   , s_tran_title6    			, sizeof(s_tran_title6		));
		memcpy  (sw.s_tran_file_id1 , s_tran_file_id1    		, sizeof(s_tran_file_id1	));
		memcpy  (sw.s_tran_file_id2 , s_tran_file_id2    		, sizeof(s_tran_file_id2	));
		memcpy  (sw.s_tran_file_id3 , s_tran_file_id3    		, sizeof(s_tran_file_id3	));
		memcpy  (sw.s_tran_file_id4 , s_tran_file_id4    		, sizeof(s_tran_file_id4	));
		memcpy  (sw.s_tran_file_id5 , s_tran_file_id5    		, sizeof(s_tran_file_id5	));
		memcpy  (sw.s_tran_file_id6 , s_tran_file_id6    		, sizeof(s_tran_file_id6	));
		
		sw.s_tran_txt_cnt = s_tran_txt_cnt;
		sw.s_tran_img_cnt = s_tran_img_cnt;
		sw.s_tran_replace_img_cnt = s_tran_replace_img_cnt;
		
		s_tran_txt_cnt =0;
		s_tran_img_cnt =0;
		s_tran_replace_img_cnt =0;
		
		vtSend.push_back(sw);
		
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "getSendData:FETCH1 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			EXEC SQL CLOSE cursor_1;
			return -1;
		}
	}
	
	EXEC SQL CLOSE cursor_1;
	
	cout<<"getSendData end\n"<<endl;
	
	return 1;
}

int CDatabaseORA::getSendDataMap(sql_context ctx, int ipart, int tpart, int  iline, map<string,string> &mapSend)
{
	//SNDSKY sw;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int s_tran_type;
	int s_tran_txt_cnt = 0;
	int s_tran_img_cnt = 0;    
	int s_tran_replace_img_cnt = 0;
	int i_part, i_tpart, i_line;
	char s_tran_pr      	 [32   +1];
	char s_tran_id      	 [20   +1];
	char s_tran_mbase_id     [40   +1];
	char s_tran_group_id     [20   +1];
	char s_tran_head    	 [1    +1];
	char s_tran_footer       [20   +1];
	char s_tran_phone   	 [15   +1];
	char s_tran_callback   	 [15   +1];
	char s_tran_msg1         [2600 +1];
	char s_tran_msg2         [120 +1];
	char s_tran_msg3         [120 +1];
	char s_tran_msg4         [120 +1];
	char s_tran_msg5         [120 +1];
	char s_tran_msg6         [120 +1];
	char s_tran_button1      [1300 +1];
	char s_tran_button2      [1300 +1];
	char s_tran_button3      [1300 +1];
	char s_tran_button4      [1300 +1];
	char s_tran_button5      [1300 +1];
	char s_tran_button6      [1300 +1];
	char s_tran_button7      [1300 +1];
	char s_tran_button8      [1300 +1];
	char s_tran_button9      [1300 +1];
	char s_tran_button10     [1300 +1];
	char s_tran_button11     [1300 +1];
	char s_tran_button12     [1300 +1];
	char s_ttl             	 [14   +1];
	char s_tran_date       	 [14   +1];
	char s_tran_title1       [60   +1];
	char s_tran_title2       [60   +1];
	char s_tran_title3       [60   +1];
	char s_tran_title4       [60   +1];
	char s_tran_title5       [60   +1];
	char s_tran_title6       [60   +1];
	char s_tran_file_id1     [120  +1];
	char s_tran_file_id2     [120  +1];
	char s_tran_file_id3     [120  +1];
	char s_tran_file_id4     [120  +1];
	char s_tran_file_id5     [120  +1];
	char s_tran_file_id6     [120  +1];
	char s_tran_replace_flag [1    +1];
	char s_tran_replace_title[200  +1];
	char s_tran_replace_msg  [2000 +1];
	char s_tran_replcae_ctn1 [255  +1];
	char s_tran_replcae_ctn2 [255  +1];
	char s_tran_replcae_ctn3 [255  +1];
	EXEC SQL END DECLARE SECTION;
	
	i_part  = ipart;
	i_tpart = tpart;
	i_line  = iline;
	char str_tran_txt_cnt			[1    +1];
	char str_tran_img_cnt  	 		[1    +1]; 
	char str_tran_replace_img_cnt	[1    +1];
	
	memset(s_tran_pr      , 0x00, sizeof(s_tran_pr      ));	
	memset(s_tran_id      , 0x00, sizeof(s_tran_id      ));	
	memset(s_tran_mbase_id, 0x00, sizeof(s_tran_mbase_id)); 
	memset(s_tran_group_id, 0x00, sizeof(s_tran_group_id)); 
	memset(s_tran_head    , 0x00, sizeof(s_tran_head    ));	
	memset(s_tran_footer  , 0x00, sizeof(s_tran_footer  )); 
	memset(s_tran_phone   , 0x00, sizeof(s_tran_phone   ));	
	memset(s_tran_callback, 0x00, sizeof(s_tran_callback)); 
	memset(s_tran_msg1    , 0x00, sizeof(s_tran_msg1    )); 
	memset(s_tran_msg2    , 0x00, sizeof(s_tran_msg2    )); 
	memset(s_tran_msg3    , 0x00, sizeof(s_tran_msg3    )); 
	memset(s_tran_msg4    , 0x00, sizeof(s_tran_msg4    )); 
	memset(s_tran_msg5    , 0x00, sizeof(s_tran_msg5    )); 
	memset(s_tran_msg6    , 0x00, sizeof(s_tran_msg6    )); 
	memset(s_tran_button1  , 0x00, sizeof(s_tran_button1  )); 
	memset(s_tran_button2  , 0x00, sizeof(s_tran_button2  )); 
	memset(s_tran_button3  , 0x00, sizeof(s_tran_button3  )); 
	memset(s_tran_button4  , 0x00, sizeof(s_tran_button4  )); 
	memset(s_tran_button5  , 0x00, sizeof(s_tran_button5  )); 
	memset(s_tran_button6  , 0x00, sizeof(s_tran_button6  )); 
	memset(s_tran_button7  , 0x00, sizeof(s_tran_button7  )); 
	memset(s_tran_button8  , 0x00, sizeof(s_tran_button8  )); 
	memset(s_tran_button9  , 0x00, sizeof(s_tran_button9  )); 
	memset(s_tran_button10  , 0x00, sizeof(s_tran_button10  )); 
	memset(s_tran_button11  , 0x00, sizeof(s_tran_button11  )); 
	memset(s_tran_button12  , 0x00, sizeof(s_tran_button12  )); 
	memset(s_ttl          , 0x00, sizeof(s_ttl          )); 
	memset(s_tran_date    , 0x00, sizeof(s_tran_date    )); 
	memset(s_tran_title1  , 0x00, sizeof(s_tran_title1  )); 
	memset(s_tran_title2  , 0x00, sizeof(s_tran_title2  )); 
	memset(s_tran_title3  , 0x00, sizeof(s_tran_title3  )); 
	memset(s_tran_title4  , 0x00, sizeof(s_tran_title4  )); 
	memset(s_tran_title5  , 0x00, sizeof(s_tran_title5  )); 
	memset(s_tran_file_id1, 0x00, sizeof(s_tran_file_id1)); 
	memset(s_tran_file_id2, 0x00, sizeof(s_tran_file_id2)); 
	memset(s_tran_file_id3, 0x00, sizeof(s_tran_file_id3)); 
	memset(s_tran_file_id4, 0x00, sizeof(s_tran_file_id4)); 
	memset(s_tran_file_id5, 0x00, sizeof(s_tran_file_id5)); 
	memset(s_tran_file_id6, 0x00, sizeof(s_tran_file_id6)); 
	memset(str_tran_txt_cnt, 0x00, sizeof(str_tran_txt_cnt)); 
	memset(str_tran_img_cnt, 0x00, sizeof(str_tran_img_cnt));  
	memset(str_tran_replace_img_cnt, 0x00, sizeof(str_tran_replace_img_cnt));
	memset(s_tran_replace_flag , 0x00, sizeof(s_tran_replace_flag )); 
	memset(s_tran_replace_title, 0x00, sizeof(s_tran_replace_title)); 
	memset(s_tran_replace_msg  , 0x00, sizeof(s_tran_replace_msg  )); 
	memset(s_tran_replcae_ctn1 , 0x00, sizeof(s_tran_replcae_ctn1 )); 
	memset(s_tran_replcae_ctn2 , 0x00, sizeof(s_tran_replcae_ctn2 )); 
	memset(s_tran_replcae_ctn3 , 0x00, sizeof(s_tran_replcae_ctn3 ));

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL 
		SELECT  TRAN_PR          	TRAN_PR,
            NVL(TRAN_ID,' ')        TRAN_ID,
            TRAN_MBASE_ID           TRAN_MBASE_ID,
            NVL(TRAN_GROUP_ID,' ')  TRAN_GROUP_ID,
            TRAN_HEAD        		TRAN_HEAD,
            NVL(TRAN_FOOTER,' ')    TRAN_FOOTER,
            TRAN_PHONE       		TRAN_PHONE,
            TRAN_CALLBACK    		TRAN_CALLBACK,
            NVL(TRAN_MSG1,' ')      TRAN_MSG1,
            NVL(TRAN_MSG2,' ')      TRAN_MSG2,
            NVL(TRAN_MSG3,' ')      TRAN_MSG3,
            NVL(TRAN_MSG4,' ')      TRAN_MSG4,
            NVL(TRAN_MSG5,' ')      TRAN_MSG5,
            NVL(TRAN_MSG6,' ')      TRAN_MSG6,
            TRAN_TYPE         		TRAN_TYPE,
            NVL(TRAN_BUTTON1,' ')    TRAN_BUTTON1,
            NVL(TRAN_BUTTON2,' ')    TRAN_BUTTON2,
            NVL(TRAN_BUTTON3,' ')    TRAN_BUTTON3,
            NVL(TRAN_BUTTON4,' ')    TRAN_BUTTON4,
            NVL(TRAN_BUTTON5,' ')    TRAN_BUTTON5,
            NVL(TRAN_BUTTON6,' ')    TRAN_BUTTON6,
            NVL(TRAN_BUTTON7,' ')    TRAN_BUTTON7,
            NVL(TRAN_BUTTON8,' ')    TRAN_BUTTON8,
            NVL(TRAN_BUTTON9,' ')    TRAN_BUTTON9,
            NVL(TRAN_BUTTON10,' ')    TRAN_BUTTON10,
            NVL(TRAN_BUTTON11,' ')    TRAN_BUTTON11,
            NVL(TRAN_BUTTON12,' ')    TRAN_BUTTON12,
            TO_CHAR(TRAN_DATE + 4320/1440 ,'YYYYMMDDHH24MISS') TTL,
            TO_CHAR(TRAN_DATE,'YYYYMMDDHH24MISS') TRAN_DATE,
            NVL(TRAN_TITLE1,' ')           	 TRAN_TITLE1,
            NVL(TRAN_TITLE2,' ')           	 TRAN_TITLE2,
            NVL(TRAN_TITLE3,' ')           	 TRAN_TITLE3,
            NVL(TRAN_TITLE4,' ')           	 TRAN_TITLE4,
            NVL(TRAN_TITLE5,' ')           	 TRAN_TITLE5,
            NVL(TRAN_TITLE6,' ')           	 TRAN_TITLE6,
            NVL(TRAN_FILE_ID1,' ')      	 TRAN_FILE_ID1,
            NVL(TRAN_FILE_ID2,' ')      	 TRAN_FILE_ID2,
            NVL(TRAN_FILE_ID3,' ')      	 TRAN_FILE_ID3,
            NVL(TRAN_FILE_ID4,' ')      	 TRAN_FILE_ID4,
            NVL(TRAN_FILE_ID5,' ')      	 TRAN_FILE_ID5,
            NVL(TRAN_FILE_ID6,' ')      	 TRAN_FILE_ID6,
			NVL(TRAN_REPLACE_FLAG,' ')       TRAN_REPLACE_FLAG,
            NVL(TRAN_REPLACE_TITLE,' ')      TRAN_REPLACE_TITLE,
            NVL(TRAN_REPLACE_MSG,' ')      	 TRAN_REPLACE_MSG,
            NVL(TRAN_REPLCAE_CTN1,' ')       TRAN_REPLCAE_CTN1,
            NVL(TRAN_REPLCAE_CTN2,' ')       TRAN_REPLCAE_CTN2,
            NVL(TRAN_REPLCAE_CTN3,' ')       TRAN_REPLCAE_CTN3
            INTO :s_tran_pr      ,
			     :s_tran_id      ,
			     :s_tran_mbase_id,
			     :s_tran_group_id,
			     :s_tran_head    ,
			     :s_tran_footer  ,
			     :s_tran_phone   ,
			     :s_tran_callback,
			     :s_tran_msg1    ,
			     :s_tran_msg2    ,
			     :s_tran_msg3    ,
			     :s_tran_msg4    ,
			     :s_tran_msg5    ,
			     :s_tran_msg6    ,
			     :s_tran_type    ,
			     :s_tran_button1  ,
			     :s_tran_button2  ,
			     :s_tran_button3  ,
			     :s_tran_button4  ,
			     :s_tran_button5  ,
			     :s_tran_button6  ,
			     :s_tran_button7  ,
			     :s_tran_button8  ,
			     :s_tran_button9  ,
			     :s_tran_button10  ,
			     :s_tran_button11  ,
			     :s_tran_button12  ,
			     :s_ttl          ,
			     :s_tran_date    ,
			     :s_tran_title1  ,
			     :s_tran_title2  ,
			     :s_tran_title3  ,
			     :s_tran_title4  ,
			     :s_tran_title5  ,
			     :s_tran_title6  ,
			     :s_tran_file_id1,
			     :s_tran_file_id2,
			     :s_tran_file_id3,
			     :s_tran_file_id4,
			     :s_tran_file_id5,
			     :s_tran_file_id6,
			     :s_tran_replace_flag,
				 :s_tran_replace_title,
				 :s_tran_replace_msg,  
				 :s_tran_replcae_ctn1, 
				 :s_tran_replcae_ctn2,
				 :s_tran_replcae_ctn3 
       FROM TBL_RCS_MSG
      WHERE TRAN_STATUS = '1'
        AND TRAN_TYPE = :i_line
        AND TRAN_DATE < SYSDATE
        AND TO_CHAR(TRAN_DATE, 'YYYYMMDDHH24MISS') > TO_CHAR(SYSDATE-4320/1440, 'YYYYMMDDHH24MISS')
        AND ROWNUM = 1
        FOR UPDATE 
    ;
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 0;
	}
		
	//EXEC SQL OPEN cursor_1;
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getSendDataMap Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		EXEC SQL ROLLBACK WORK;
		return -1;
	}
	
	 
	 
	 //sprintf(tmpLog3, "shs getSendData:FETCH1 [%s][%d][%.150s]", s_tran_pr,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		//	log3(tmpLog3, 0, 0);
		
	 trim3(s_tran_pr      , strlen(s_tran_pr      ));	
	 trim3(s_tran_id      , strlen(s_tran_id      ));	
	 trim3(s_tran_mbase_id, strlen(s_tran_mbase_id)); 
	 trim3(s_tran_group_id, strlen(s_tran_group_id)); 
	 trim3(s_tran_head    , strlen(s_tran_head    ));	
	 trim3(s_tran_footer  , strlen(s_tran_footer  )); 
	 trim3(s_tran_phone   , strlen(s_tran_phone   ));	
	 trim3(s_tran_callback, strlen(s_tran_callback)); 
	 trim3(s_tran_msg1    , strlen(s_tran_msg1    )); 
	 trim3(s_tran_msg2    , strlen(s_tran_msg2    )); 
	 trim3(s_tran_msg3    , strlen(s_tran_msg3    )); 
	 trim3(s_tran_msg4    , strlen(s_tran_msg4    )); 
	 trim3(s_tran_msg5    , strlen(s_tran_msg5    )); 
	 trim3(s_tran_msg6    , strlen(s_tran_msg6    )); 
	 trim3(s_tran_button1  , strlen(s_tran_button1  )); 
	 trim3(s_tran_button2  , strlen(s_tran_button2  )); 
	 trim3(s_tran_button3  , strlen(s_tran_button3  )); 
	 trim3(s_tran_button4  , strlen(s_tran_button4  )); 
	 trim3(s_tran_button5  , strlen(s_tran_button5  )); 
	 trim3(s_tran_button6  , strlen(s_tran_button6  )); 
	 trim3(s_tran_button7  , strlen(s_tran_button7  )); 
	 trim3(s_tran_button8  , strlen(s_tran_button8  )); 
	 trim3(s_tran_button9  , strlen(s_tran_button9  )); 
	 trim3(s_tran_button10  , strlen(s_tran_button10  )); 
	 trim3(s_tran_button11  , strlen(s_tran_button11  )); 
	 trim3(s_tran_button12  , strlen(s_tran_button12  )); 
	 trim3(s_ttl          , strlen(s_ttl          )); 
	 trim3(s_tran_date    , strlen(s_tran_date    )); 
	 trim3(s_tran_title1  , strlen(s_tran_title1  )); 
	 trim3(s_tran_title2  , strlen(s_tran_title2  )); 
	 trim3(s_tran_title3  , strlen(s_tran_title3  )); 
	 trim3(s_tran_title4  , strlen(s_tran_title4  )); 
	 trim3(s_tran_title5  , strlen(s_tran_title5  )); 
	 trim3(s_tran_title6  , strlen(s_tran_title6  )); 
	 trim3(s_tran_file_id1, strlen(s_tran_file_id1)); 
	 trim3(s_tran_file_id2, strlen(s_tran_file_id2)); 
	 trim3(s_tran_file_id3, strlen(s_tran_file_id3)); 
	 trim3(s_tran_file_id4, strlen(s_tran_file_id4)); 
	 trim3(s_tran_file_id5, strlen(s_tran_file_id5)); 
	 trim3(s_tran_file_id6, strlen(s_tran_file_id6)); 
	 trim3(s_tran_replace_flag ,strlen(s_tran_replace_flag ));
	 trim3(s_tran_replace_title,strlen(s_tran_replace_title));
	 trim3(s_tran_replace_msg  ,strlen(s_tran_replace_msg  ));
	 trim3(s_tran_replcae_ctn1 ,strlen(s_tran_replcae_ctn1 ));
	 trim3(s_tran_replcae_ctn2 ,strlen(s_tran_replcae_ctn2 ));
	 trim3(s_tran_replcae_ctn3 ,strlen(s_tran_replcae_ctn3 ));
	 
	 if(strlen(s_tran_msg1) > 0 )
	 	s_tran_txt_cnt +=1;
	 if(strlen(s_tran_msg2) > 0 )
	 	s_tran_txt_cnt +=1;	
	 if(strlen(s_tran_msg3) > 0 )
	 	s_tran_txt_cnt +=1;	
	 if(strlen(s_tran_msg4) > 0 )
	 	s_tran_txt_cnt +=1;		
	 if(strlen(s_tran_msg5) > 0 )
	 	s_tran_txt_cnt +=1;		
	 if(strlen(s_tran_msg6) > 0 )
	 	s_tran_txt_cnt +=1;						
	 	
	 if(strlen(s_tran_file_id1) > 0 )
	 	s_tran_img_cnt +=1;
	 if(strlen(s_tran_file_id2) > 0 )
	 	s_tran_img_cnt +=1;	
	 if(strlen(s_tran_file_id3) > 0 )
	 	s_tran_img_cnt +=1;	
	 if(strlen(s_tran_file_id4) > 0 )
	 	s_tran_img_cnt +=1;		
	 if(strlen(s_tran_file_id5) > 0 )
	 	s_tran_img_cnt +=1;		
	 if(strlen(s_tran_file_id6) > 0 )
	 	s_tran_img_cnt +=1;	
	 	
	 if(strlen(s_tran_replcae_ctn1) > 0 )	
	   	s_tran_replace_img_cnt +=1;	
	 if(strlen(s_tran_replcae_ctn2) > 0 )	
	   	s_tran_replace_img_cnt +=1;	
	 if(strlen(s_tran_replcae_ctn3) > 0 )	
	   	s_tran_replace_img_cnt +=1;	
	 	
	 //sprintf(tmpLog3, "getSendData:sw insert before::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		//	log3(tmpLog3, 0, 0);

		//memset(&sw, 0x00, sizeof(sw));
		
		mapSend["tran_pr" 	   ] = s_tran_pr  		;
		mapSend["tran_id" 	   ] = s_tran_id  		;
		mapSend["tran_mbase_id"] = s_tran_mbase_id  ;
		mapSend["tran_group_id"] = s_tran_group_id  ;
		mapSend["tran_head"    ] = s_tran_head  	;
		mapSend["tran_footer"  ] = s_tran_footer  	;
		mapSend["tran_phone"   ] = s_tran_phone     ;
		mapSend["tran_callback"] = s_tran_callback  ;
		mapSend["tran_msg1"    ] = s_tran_msg1      ;
		mapSend["tran_msg2"    ] = s_tran_msg2      ;
		mapSend["tran_msg3"    ] = s_tran_msg3      ;
		mapSend["tran_msg4"    ] = s_tran_msg4      ;
		mapSend["tran_msg5"    ] = s_tran_msg5      ;
		mapSend["tran_msg6"    ] = s_tran_msg6      ;
		mapSend["tran_button1"  ] = s_tran_button1    ;
		mapSend["tran_button2"  ] = s_tran_button2    ;
		mapSend["tran_button3"  ] = s_tran_button3    ;
		mapSend["tran_button4"  ] = s_tran_button4    ;
		mapSend["tran_button5"  ] = s_tran_button5    ;
		mapSend["tran_button6"  ] = s_tran_button6    ;
		mapSend["tran_button7"  ] = s_tran_button7    ;
		mapSend["tran_button8"  ] = s_tran_button8    ;
		mapSend["tran_button9"  ] = s_tran_button9    ;
		mapSend["tran_button10"  ] = s_tran_button10    ;
		mapSend["tran_button11"  ] = s_tran_button11    ;
		mapSend["tran_button12"  ] = s_tran_button12    ;
		mapSend["ttl"     	   ] = s_ttl      		;
		mapSend["tran_date"    ] = s_tran_date      ;
		mapSend["tran_title1"  ] = s_tran_title1    ;
		mapSend["tran_title2"  ] = s_tran_title2    ;
		mapSend["tran_title3"  ] = s_tran_title3    ;
		mapSend["tran_title4"  ] = s_tran_title4    ;
		mapSend["tran_title5"  ] = s_tran_title5    ;
		mapSend["tran_title6"  ] = s_tran_title6    ;
		mapSend["tran_file_id1"] = s_tran_file_id1  ;
		mapSend["tran_file_id2"] = s_tran_file_id2  ;
		mapSend["tran_file_id3"] = s_tran_file_id3  ;
		mapSend["tran_file_id4"] = s_tran_file_id4  ;
		mapSend["tran_file_id5"] = s_tran_file_id5  ;
		mapSend["tran_file_id6"] = s_tran_file_id6  ;
		mapSend["tran_replace_flag" ] = s_tran_replace_flag   ;
		mapSend["tran_replace_title"] = s_tran_replace_title  ;
		mapSend["tran_replace_msg"  ] = s_tran_replace_msg    ;
		mapSend["tran_replcae_ctn1" ] = s_tran_replcae_ctn1   ;
		mapSend["tran_replcae_ctn2" ] = s_tran_replcae_ctn2   ;
		mapSend["tran_replcae_ctn3" ] = s_tran_replcae_ctn3   ;
		
		sprintf(str_tran_txt_cnt,"%d",s_tran_txt_cnt);
		sprintf(str_tran_img_cnt,"%d",s_tran_img_cnt);
		sprintf(str_tran_replace_img_cnt,"%d",s_tran_replace_img_cnt);
		
		mapSend["tran_txt_cnt"] = str_tran_txt_cnt  ;
		mapSend["tran_img_cnt"] = str_tran_img_cnt  ; 
		mapSend["tran_replace_img_cnt"] = str_tran_replace_img_cnt  ;
		
		s_tran_txt_cnt =0;
		s_tran_img_cnt =0;    
		s_tran_replace_img_cnt =0; 
		
		EXEC SQL WHENEVER SQLERROR CONTINUE;
		EXEC SQL CONTEXT USE :ctx;
		EXEC SQL
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS  = 6
		 WHERE TRAN_PR = TRIM(:s_tran_pr);
		 	
		if (sqlca.sqlcode == 1403 )
		{
			EXEC SQL COMMIT WORK;
			return 1;
		}
			
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "getSendDataMap:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			//EXEC SQL ROLLBACK WORK;
			return -1;
		}
		
		EXEC SQL COMMIT WORK; 	
	
	
	cout<<"getSendData end\n"<<endl;
	
	return 1;
}

int CDatabaseORA::getCtnSendDataMap(sql_context ctx, int ipart, int tpart, int  iline, map<string,string> &mapCtnSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int s_tran_type;
	int i_part, i_tpart, i_line;
	int s_content_seq;
	char s_tran_pr      	  [32   +1];
	char s_content_type       [3    +1];
	char s_content_name       [255  +1];
	char s_content_svc	      [3    +1];
	char s_ttl             	 [14   +1];
	char s_tran_date       	 [14   +1];
	EXEC SQL END DECLARE SECTION;
	
	i_part  = ipart;
	i_tpart = tpart;
	i_line  = iline;
	
	memset(s_tran_pr      	      , 0x00, sizeof(s_tran_pr      ));	
	memset(s_content_type         , 0x00, sizeof(s_content_type ));	
	memset(s_content_name         , 0x00, sizeof(s_content_name )); 
	memset(s_content_svc	      , 0x00, sizeof(s_content_svc	)); 
	memset(s_ttl          , 0x00, sizeof(s_ttl          )); 
	memset(s_tran_date    , 0x00, sizeof(s_tran_date    )); 

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL 
		SELECT  TRAN_PR					TRAN_PR,
     	      	CONTENT_TYPE        	CONTENT_TYPE,
				NVL(CONTENT_NAME,' ')	CONTENT_NAME,       
				CONTENT_SVC				CONTENT_SVC,	      
				TRAN_TYPE         		TRAN_TYPE,
     	      	TO_CHAR(TRAN_DATE + 4320/1440 ,'YYYYMMDDHH24MISS') TTL,
     	      	TO_CHAR(TRAN_DATE,'YYYYMMDDHH24MISS') TRAN_DATE
     	   INTO :s_tran_pr      	,
			    :s_content_type    ,
			    :s_content_name    ,
			    :s_content_svc	    ,
			    :s_tran_type    	,
			    :s_ttl          	,
			    :s_tran_date       	
     	   FROM TBL_RCS_CTN_CONTENTS
     	  WHERE TRAN_STATUS = '1'
     	    AND TRAN_TYPE = :i_line
     	    AND TRAN_DATE < SYSDATE
     	    AND TO_CHAR(TRAN_DATE, 'YYYYMMDDHH24MISS') > TO_CHAR(SYSDATE-4320/1440, 'YYYYMMDDHH24MISS')
     	    AND ROWNUM  = 1
     	    FOR UPDATE 
	;
	
	if(sqlca.sqlcode == 1403)
	{
		EXEC SQL COMMIT; 
		return 0;
	}
	
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getCtnSendDataMap Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		EXEC SQL ROLLBACK; 
		return -1;
	}
	
	
	
	//sprintf(tmpLog3, "shs getCtnSendData:FETCH2 [%s][%d][%.150s]", s_tran_pr,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
	//	log3(tmpLog3, 0, 0);
	
	
	trim3(s_tran_pr      , strlen(s_tran_pr      ));	
	trim3(s_content_type , strlen(s_content_type ));	
	trim3(s_content_name , strlen(s_content_name )); 
	trim3(s_content_svc	 , strlen(s_content_svc  )); 
	trim3(s_ttl          , strlen(s_ttl          )); 
	trim3(s_tran_date    , strlen(s_tran_date    )); 
	
	
		
	
	mapCtnSend["tran_pr"] = s_tran_pr  ;
	mapCtnSend["content_type"] = s_content_type  ;
	mapCtnSend["content_name"] = s_content_name  ;
	mapCtnSend["content_svc"] = s_content_svc  ;
	mapCtnSend["ttl"] = s_ttl  ;
	mapCtnSend["tran_date"] = s_tran_date  ;
	
	sprintf(tmpLog3, "getCtnSendDataMap shs2::[%s][%s]", s_content_name,mapCtnSend["content_name"].c_str());
		log3(tmpLog3, 0, 0);
	
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getCtnSendDataMap Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		EXEC SQL ROLLBACK WORK;
		return -1;
	}
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		UPDATE TBL_RCS_CTN_CONTENTS
		   SET TRAN_STATUS  = 6
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	//sprintf(tmpLog3, "shs setWaitUpdate:TBL_RCS_MSG UPDATE::[%s][%s][%d]", s_tran_pr,s_tran_status,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
		
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getCtnSendDataMap:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		//EXEC SQL ROLLBACK WORK;
		return -1;
	}

	EXEC SQL COMMIT WORK;
		
	
	//EXEC SQL COMMIT; 
	return 1;
}


int CDatabaseORA::getCtnSendData(sql_context ctx, int ipart, int tpart, int  iline, vector<SNDCTNSKY>& vtCtnSend)
{
	SNDCTNSKY scw;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int s_tran_type;
	int i_part, i_tpart, i_line;
	int s_content_seq;
	char s_tran_pr      	  [32   +1];
	char s_content_type       [3    +1];
	char s_content_name       [255  +1];
	char s_content_svc	      [3    +1];
	char s_ttl             	 [14   +1];
	char s_tran_date       	 [14   +1];
	EXEC SQL END DECLARE SECTION;
	
	i_part  = ipart;
	i_tpart = tpart;
	i_line  = iline;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL DECLARE cursor_2 CURSOR FOR
		SELECT  TRAN_PR					TRAN_PR,
     	      	CONTENT_TYPE        	CONTENT_TYPE,
				NVL(CONTENT_NAME,' ')	CONTENT_NAME,       
				CONTENT_SVC				CONTENT_SVC,	      
				TRAN_TYPE         		TRAN_TYPE,
     	      	TO_CHAR(TRAN_DATE + 4320/1440 ,'YYYYMMDDHH24MISS') TTL,
     	      	TO_CHAR(TRAN_DATE,'YYYYMMDDHH24MISS') TRAN_DATE
     	   FROM TBL_RCS_CTN_CONTENTS
     	  WHERE TRAN_STATUS = '1'
     	    AND TRAN_TYPE = :i_line
     	    AND TRAN_DATE < SYSDATE
     	    AND TO_CHAR(TRAN_DATE, 'YYYYMMDDHH24MISS') > TO_CHAR(SYSDATE-4320/1440, 'YYYYMMDDHH24MISS')
     	    AND ROWNUM  < 101
     	    FOR UPDATE
	;
	EXEC SQL OPEN cursor_2;
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "getCtnSendData:CURSOR2 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		//EXEC SQL ROLLBACK; 
		return -1;
	}
	
	for(int i = 0;;i++)
	{
		memset(s_tran_pr      	      , 0x00, sizeof(s_tran_pr      ));	
	    memset(s_content_type         , 0x00, sizeof(s_content_type ));	
	    memset(s_content_name         , 0x00, sizeof(s_content_name )); 
	    memset(s_content_svc	      , 0x00, sizeof(s_content_svc	)); 
	    memset(s_ttl          , 0x00, sizeof(s_ttl          )); 
	    memset(s_tran_date    , 0x00, sizeof(s_tran_date    )); 
	    
	    
		EXEC SQL FETCH cursor_2
			INTO :s_tran_pr      	,
			     :s_content_type    ,
			     :s_content_name    ,
			     :s_content_svc	    ,
			     :s_tran_type    	,
			     :s_ttl          	,
			     :s_tran_date    
		;

		
		if(sqlca.sqlcode == 1403)
		{
			EXEC SQL CLOSE cursor_2;  
			//EXEC SQL COMMIT; 
			return 1;
		}
		
		//sprintf(tmpLog3, "shs getCtnSendData:FETCH2 [%s][%d][%.150s]", s_tran_pr,sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		//	log3(tmpLog3, 0, 0);
		
		
		TrimR(s_tran_pr      , sizeof(s_tran_pr      ));	
	    TrimR(s_content_type , sizeof(s_content_type      ));	
	    TrimR(s_content_name , sizeof(s_content_name)); 
	    TrimR(s_content_svc	 , sizeof(s_content_svc)); 
	    TrimR(s_ttl          , sizeof(s_ttl          )); 
	    TrimR(s_tran_date    , sizeof(s_tran_date    )); 
	    
	    
	    memset(&scw, 0x00, sizeof(scw));
		
		snprintf(scw.s_tran_pr 				, sizeof(s_tran_pr   	 ), s_tran_pr  							 );
		snprintf(scw.s_content_type 			, sizeof(s_content_type   	 ), s_content_type  							 );
		memcpy  (scw.s_content_name     		, s_content_name      			, sizeof(s_content_name     	));
		snprintf(scw.s_content_svc     				, sizeof(s_content_svc     	 	 ), s_content_svc      							 );
		snprintf(scw.s_ttl     				, sizeof(s_ttl     	 	 ), s_ttl      							 );
		snprintf(scw.s_tran_date     	, sizeof(s_tran_date 	 ), s_tran_date      				 );
		
		vtCtnSend.push_back(scw);
		
		
		
		if(sqlca.sqlcode != 0)
		{
			sprintf(tmpLog3, "getCtnSendData:FETCH2 Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			//EXEC SQL ROLLBACK; 
			EXEC SQL CLOSE cursor_2;
			return -1;
		}
		
	}
	
	//EXEC SQL COMMIT; 
	EXEC SQL CLOSE cursor_2;

	return 1;
}

int CDatabaseORA::setWaitUpdateMap(sql_context ctx, char* status, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	char s_tran_status [1+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(s_tran_pr			, 0x00, sizeof(s_tran_pr));
	memset(s_tran_status 	, 0x00, sizeof(s_tran_status ));
	
	sprintf(s_tran_pr,"%s", mapSend["s_tran_pr"].c_str());
	strcpy(s_tran_status 	, status          );
	
	TrimR(s_tran_pr    , sizeof(s_tran_pr    ));

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS  = :s_tran_status
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "setWaitUpdate:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setWaitUpdate(sql_context ctx, char* status, SNDSKY& vtSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	char s_tran_status [1+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(s_tran_pr			, 0x00, sizeof(s_tran_pr));
	memset(s_tran_status 	, 0x00, sizeof(s_tran_status ));
	
	sprintf(s_tran_pr,"%s", vtSend.s_tran_pr);
	strcpy(s_tran_status 	, status          );
	
	TrimR(s_tran_pr    , sizeof(s_tran_pr    ));

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS  = :s_tran_status
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "setWaitUpdate:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}


int CDatabaseORA::setCtnWaitUpdate(sql_context ctx, char* status, SNDCTNSKY& vtCtnSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	char s_tran_status [1+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(s_tran_pr			, 0x00, sizeof(s_tran_pr));
	memset(s_tran_status 	, 0x00, sizeof(s_tran_status ));
	
	sprintf(s_tran_pr,"%s", vtCtnSend.s_tran_pr);
	strcpy(s_tran_status 	, status          );
	
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		UPDATE TBL_RCS_CTN_CONTENTS
		   SET TRAN_STATUS  = :s_tran_status
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	//sprintf(tmpLog3, "shs setWaitUpdate:TBL_RCS_MSG UPDATE::[%s][%s][%d]", s_tran_pr,s_tran_status,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
		
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "setWaitUpdate:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setCtnWaitUpdateMap(sql_context ctx, char* status, map<string,string> &mapCtnSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	char s_tran_status [1+1];
	EXEC SQL END DECLARE SECTION;
	
	memset(s_tran_pr			, 0x00, sizeof(s_tran_pr));
	memset(s_tran_status 	, 0x00, sizeof(s_tran_status ));
	
	sprintf(s_tran_pr,"%s", mapCtnSend["tran_pr"].c_str());
	strcpy(s_tran_status 	, status          );
	
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		UPDATE TBL_RCS_CTN_CONTENTS
		   SET TRAN_STATUS  = :s_tran_status
		 WHERE TRAN_PR = TRIM(:s_tran_pr)
	;
	
	//sprintf(tmpLog3, "shs setWaitUpdate:TBL_RCS_MSG UPDATE::[%s][%s][%d]", s_tran_pr,s_tran_status,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
		
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
		
	if(sqlca.sqlcode != 0)
	{
		sprintf(tmpLog3, "setWaitUpdate:TBL_RCS_MSG UPDATE Err::[%s][%d][%.150s]", s_tran_pr, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setSndAckDataMap(sql_context ctx, map<string,string> &mapSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	EXEC SQL END DECLARE SECTION;

	memset(s_tran_pr, 0x00, sizeof(s_tran_pr));

	//vector<string>::iterator itrData;
	//itrData = vtSndAck.begin();
	/*
	 * vtSndAck list
	 * 0:KEY
	 * 1:CODE
	 * 2:DESC
	 */
	strncpy(s_tran_pr, mapSend["tran_pr"].c_str(), sizeof(s_tran_pr)-1);
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS   = '2'
		     , TRAN_SENDDATE = SYSDATE
		 WHERE TRAN_PR  = TRIM(:s_tran_pr)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//sprintf(tmpLog3, "shs setSndAckData:TBL_RCS_MSG UPDATE::[%s][%d]", s_tran_pr,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
	
	if (sqlca.sqlcode != 0 )
	{
			sprintf(tmpLog3, "setSndAckData:TBL_RCS_MSG UPDATE Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setSndAckData(sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	EXEC SQL END DECLARE SECTION;

	memset(s_tran_pr, 0x00, sizeof(s_tran_pr));

	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();
	/*
	 * vtSndAck list
	 * 0:KEY
	 * 1:CODE
	 * 2:DESC
	 */
	strncpy(s_tran_pr, string(*(itrData)).c_str(), sizeof(s_tran_pr)-1);
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS   = '2'
		     , TRAN_SENDDATE = SYSDATE
		 WHERE TRAN_PR  = TRIM(:s_tran_pr)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//sprintf(tmpLog3, "shs setSndAckData:TBL_RCS_MSG UPDATE::[%s][%d]", s_tran_pr,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
	
	if (sqlca.sqlcode != 0 )
	{
			sprintf(tmpLog3, "setSndAckData:TBL_RCS_MSG UPDATE Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}


int CDatabaseORA::setCtnSndAckData(sql_context ctx, vector<string>& vtCtnSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char s_tran_pr[32+1];
	EXEC SQL END DECLARE SECTION;

	memset(s_tran_pr, 0x00, sizeof(s_tran_pr));

	vector<string>::iterator itrData;
	itrData = vtCtnSndAck.begin();
	/*
	 * vtSndAck list
	 * 0:KEY
	 * 1:CODE
	 * 2:DESC
	 */
	strncpy(s_tran_pr, string(*(itrData)).c_str(), sizeof(s_tran_pr)-1);
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		UPDATE TBL_RCS_CTN_CONTENTS
		   SET TRAN_STATUS   = '2'
		     , TRAN_SENDDATE = SYSDATE
		 WHERE TRAN_PR  = TRIM(:s_tran_pr)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//sprintf(tmpLog3, "shs setSndAckData:TBL_RCS_MSG UPDATE::[%s][%d]", s_tran_pr,sqlca.sqlcode);
	//	log3(tmpLog3, 0, 0);
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
	
	if (sqlca.sqlcode != 0 )
	{
			sprintf(tmpLog3, "setSndAckData:TBL_RCS_MSG UPDATE Err::[%d][%.150s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
			log3(tmpLog3, 0, 0);
			return -1;
	}
	
	EXEC SQL COMMIT WORK;
	return 1;
}

/*
int CDatabaseORA::setSndAckData(sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int nSqlCod, nRetCod, mMsgSeq;
	char sRptCod[2+1], sErrMsg[256];
	EXEC SQL END DECLARE SECTION;

	nSqlCod = nRetCod = mMsgSeq = 0;
	memset(sRptCod, 0x00, sizeof(sRptCod));
	memset(sErrMsg, 0x00, sizeof(sErrMsg));
	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();
	mMsgSeq = atoi(string(*itrData).c_str());
	strcpy(sRptCod, string(*(itrData+1)).c_str());

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		bcdba.proc_set_ackdata(:mMsgSeq, :sRptCod, :nRetCod, :nSqlCod, :sErrMsg);
	END;
	END-EXEC;

	if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114) 
		return -1;
	if (nRetCod!=0) {
		cout << "setSndAckDataErr::["<<mMsgSeq<<"]["<<sRptCod<<"]["<<nRetCod<<"]["<<nSqlCod<<"]["<<sErrMsg<<"]" << endl;
		return -1;
	}
	else
		return 1;
}
*/

int CDatabaseORA::setReportData(sql_context ctx, vector<string>& vtReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
		struct sqlca sqlca;
		int nSqlCod, nRetCod;
		char sMmsId[32+1];
		char sDlvDate[14+1];
		char sResCode[5+1];
	EXEC SQL END DECLARE SECTION;


	nSqlCod = nRetCod = 0;
	memset(sDlvDate, 0x00, sizeof(sDlvDate));
	memset(sResCode, 0x00, sizeof(sResCode));
	
	vector<string>::iterator itrData;
	itrData = vtReport.begin();
	strcpy(sMmsId, string(*itrData).c_str());
	strcpy(sDlvDate, string(*(itrData+2)).c_str());
	strncpy(sResCode, string(*(itrData+1)).c_str(), 5);
	//strcpy(sResCode, string(*(itrData+1)).c_str());
	
	//sprintf(tmpLog3, "shs setReportData ::[%s][%s][%s]", sMmsId, sDlvDate,sResCode);
	//	log3(tmpLog3, 0, 0);
	

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		UPDATE TBL_RCS_MSG
		   SET TRAN_STATUS   = '3'
		     , TRAN_REPORTDATE = TO_DATE(:sDlvDate,'YYYYMMDDHH24MISS')
		     , TRAN_RSLTDATE = SYSDATE
		     , TRAN_RSLT = :sResCode
		 WHERE TRAN_PR  = TRIM(:sMmsId)
		 	 AND TRAN_STATUS   != '3'
	;
	
	//if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114) 
	//	return -1;
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}

	if (sqlca.sqlcode!=0) {
		//cout << "setReportDataErr::]["<<nRetCod<<"]["<<nSqlCod<<"]["<<sErrMsg<<"]" << endl;
		sprintf(tmpLog3, "setReportData Err::sMmsId[%s][%d][%.150s]", sMmsId, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

int CDatabaseORA::setCtnReportData(sql_context ctx, vector<string>& vtCtnReport)
{
	EXEC SQL BEGIN DECLARE SECTION;
		struct sqlca sqlca;
		int nSqlCod, nRetCod;
		char sMmsId[32+1];
		char sFileId[100+1];
		char sExpiryDate[40+1];
		char sResCode[5+1];
	EXEC SQL END DECLARE SECTION;
	
	nSqlCod = nRetCod = 0;
	memset(sFileId, 0x00, sizeof(sFileId));
	memset(sExpiryDate, 0x00, sizeof(sExpiryDate));
	memset(sResCode, 0x00, sizeof(sResCode));
	
	vector<string>::iterator itrData;
	itrData = vtCtnReport.begin();
	
	strcpy(sMmsId, string(*itrData).c_str());
	strcpy(sFileId, string(*(itrData+2)).c_str());
	strcpy(sExpiryDate, string(*(itrData+4)).c_str());
	strncpy(sResCode, string(*(itrData+1)).c_str(), 5);
	
	//strcpy(sResCode, string(*(itrData+1)).c_str());
	
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL
		//UPDATE BCMSG.KB_MMS_GRP
		UPDATE TBL_RCS_CTN_CONTENTS
		   SET TRAN_STATUS   = '3'
		     , TRAN_REPORTDATE = SYSDATE
		     , TRAN_RSLTDATE = SYSDATE
		     , CONTENT_FILE_ID = :sFileId
		     , CONTENT_EXPIRY_DATE = :sExpiryDate
		     , CONTENT_RES_CODE = :sResCode
		 WHERE TRAN_PR  = TRIM(:sMmsId)
		 	 AND TRAN_STATUS   != '3'
	;
	
	if (sqlca.sqlcode == 1403 )
	{
		EXEC SQL COMMIT WORK;
		return 1;
	}
	
	//if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114) 
	//	return -1;

	if (sqlca.sqlcode!=0) {
		//cout << "setReportDataErr::]["<<nRetCod<<"]["<<nSqlCod<<"]["<<sErrMsg<<"]" << endl;
		sprintf(tmpLog3, "setCtnReportData Err::sMmsId[%s][%d][%.150s]", sMmsId, sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc);
		log3(tmpLog3, 0, 0);
		return -1;
	}

	EXEC SQL COMMIT WORK;
	return 1;
}

/*
int CDatabaseORA::setTranDate(sql_context ctx, int mms_id)
{
	EXEC SQL BEGIN DECLARE SECTION;
		struct sqlca sqlca;

		int nMmsId;
	EXEC SQL END DECLARE SECTION;


	nMmsId = mms_id;
#ifdef DEBUG
cout << "+++++++++++++++++++++setTranDate mmsid:[" << nMmsId << "]" << endl;
#endif

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		UPDATE TBMMS_SEND 
		SET TRAN_DATE = to_char(sysdate, 'yyyymmddhh24miss')
		WHERE MMS_ID = :nMmsId
		;
	COMMIT;
	END;
	END-EXEC;

#ifdef DEBUG
cout << "+++++++++++++++++++++setTranDate sqlcode:[" << sqlca.sqlcode << "]" << endl;
#endif
	if (sqlca.sqlcode != 0) {
		cout << "setTranDate sqlcode:[" << sqlca.sqlcode << "]" << endl;
		return -1;
	}

	return 0;
}
*/

void CDatabaseORA::log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}

char* CDatabaseORA::trim3(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

}
