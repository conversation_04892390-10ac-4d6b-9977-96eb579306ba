/*****************************************************************************
 * File Name   : mo_process.h
 * Author      : kskyb.com
 * Date        : 2008.09.11
 * Description : MO_PROCESS Header & Define
 *****************************************************************************/
#ifndef _MO_PROCESS_H_
#define _MO_PROCESS_H_

#include <string>
using namespace std;

class CMoData {
public:
	int  nSeqNo				  ;
	char szSerial		[20+1];
	char szMoPhone		[20+1];
	char sz<PERSON>allBack		[20+1];
	char sz<PERSON>rg<PERSON><PERSON>		[20+1];
	char szMoMsg		[128+1];
	char szMoRecvDate	[20+1];
	char szReg<PERSON><PERSON>		[16+1];
	char szSendFlag		[160+1]; // tran_etc1 NULL or Y
};

class CTopCustData {
public:
	char szCustName		[20+1];
	char szCustSocn		[20+1];
	char szDstAddr		[25+1];
	char szCustMrNo		[9+1];
	char szPoint		[16+1];
	char szGender		[ 1+1];
	char szBirth		[ 2+1];
	int nPoint;
};

class CTopMerchData {
public:
/*
	char szDwSrcKey[20+1];
	char szDwType[1+1];
	char szMrkType[1+1];
	char szCupId[16+1];
	char szPerVal[5+1];
	char szZipChar[3+1];
	char szMerchInfo[20+1];
*/
	char szLocale		[50];
	char szBizcat		[50];
	char szLocatInfo	[20+1];
	char szShopInfo		[40+1];
	char szTelInfo		[12+1];
	char szLocCode      [ 6+1];
	char szBizCode      [ 4+1];
	char szMerCode      [ 9+1];
	char szBncCode      [ 7+1];
};

class CSmsData {
public:
	char szJobCode		[3+1];
	char szMsgTemp		[100+1];
	char szTranPhone	[15+1];
	char szCallBack		[12+1];
	char szTranMsg		[86+1];
};

#endif //_MO_PROCESS_H_
