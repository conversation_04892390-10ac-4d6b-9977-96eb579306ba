/*
 * Program Name   : group_shm_info.h
 * Remarks        : Shared Memory Information LAYOUT
 * Initial Coding : 97/11/28 InMok,Song
 */

#ifndef __GROUP_SHM_H__
#define __GROUP_SHM_H__

#ifdef __cplusplus
extern "C" {
#endif

#define GROUP_SHM_LEN           6144

struct _group_shm_info
{
    char                        area[GROUP_SHM_LEN];
};

struct _group_shm_info *        group_shm_info;

#ifdef __cplusplus
}
#endif

#endif /*  __GROUP_SHM_H__  */

/* END */
