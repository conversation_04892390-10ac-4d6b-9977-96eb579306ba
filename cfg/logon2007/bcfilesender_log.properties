log4j.rootLogger = DEBUG, COMMON, CONSOLE
log4j.category.default = DEBUG, COMMON, CONSOLE
log4j.additivity.default = false

log4j.appender.CONSOLE = org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.layout = org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.ConversionPattern=%-5p %l - %m%n

log4j.category.COMMON = INFO, COMMON
log4j.additivity.COMMON = false
log4j.appender.COMMON = org.apache.log4j.DailyRollingFileAppender
log4j.appender.COMMON.Append = true
log4j.appender.COMMON.File = /user/neomms/LOG/bcfilesender_monitor.log
log4j.appender.COMMON.DatePattern = '.'yyyyMMdd
log4j.appender.COMMON.layout = org.apache.log4j.PatternLayout
log4j.appender.COMMON.layout.ConversionPattern = [%d{HH:mm:ss.SSS}]: %m %n
log4j.appender.COMMON.Threshold = INFO

log4j.category.TRANSACTION = INFO, TRANSACTION
log4j.additivity.TRANSACTION = false
log4j.appender.TRANSACTION = org.apache.log4j.DailyRollingFileAppender
log4j.appender.TRANSACTION.Append = true
log4j.appender.TRANSACTION.File = /user/neomms/LOG/bcfilesender_tran.log
log4j.appender.TRANSACTION.DatePattern = '.'yyyyMMdd
log4j.appender.TRANSACTION.layout = org.apache.log4j.PatternLayout
log4j.appender.TRANSACTION.layout.ConversionPattern = [%d{HH:mm:ss.SSS}]: %m %n
log4j.appender.TRANSACTION.Threshold = INFO
