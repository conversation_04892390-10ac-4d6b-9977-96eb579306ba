/*
 * DatabaseORA.cpp
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#include "DatabaseORA.h"
#include <sqlca.h>
#include <iostream>
using namespace std;

char tmpLog3[1024];
void log3(char *buf, int st, int err);

namespace KSKYB
{
int CDatabaseORA::setEnableThreads()
{
	m_bThread = true;
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL ENABLE THREADS;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::setEnableThreads() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::initThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT ALLOCATE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::initThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::freeThread(sql_context& ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	sql_context pCtx;
	EXEC SQL END DECLARE SECTION;

	pCtx = ctx;
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT FREE :pCtx;
	ctx = pCtx;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::freeThread() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	return 1;
}

int CDatabaseORA::connectToOracle(sql_context ctx, char* szUID, char* szDSN)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	char szConnInf[20+1], szConnDsn[20+1];
	EXEC SQL END DECLARE SECTION;

	if ((szUID == NULL) || (szDSN == NULL)) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[szUID or szDSN is NULL]");
		log3(tmpLog3, 0, 0);
		return -1;
	}
	memset(szConnInf, 0x00, sizeof(szConnInf));
	memset(szConnDsn, 0x00, sizeof(szConnDsn));
	strcpy(szConnInf, szUID);
	strcpy(szConnDsn, szDSN);

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :szConnInf USING :szConnDsn;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::connectToOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::connectToOracle() Success" << endl;
	return 1;
}

int CDatabaseORA::closeFromOracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL COMMIT WORK RELEASE;

	if (sqlca.sqlcode != 0) {
		sprintf(tmpLog3, "CDatabaseORA::closeFromOracle() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		cout << "CDatabaseORA::closeFromOracle() Success" << endl;
	return 1;
}

long long CDatabaseORA::getSendData(sql_context ctx, int nQID, vector<string>& vtSend)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int telco_id;
	long long  msg_id;
	char talk_id[40+1];
	char dstaddr[12+1];
	char template_cd[10+1];
	char msg_body[1000+1];
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	struct sqlca sqlca;
	EXEC SQL END DECLARE SECTION;

	telco_id = nQID;
	memset(ot_sqlmsg,0x00, sizeof(ot_sqlmsg));
	memset(dstaddr,0x00, sizeof(dstaddr));
	memset(template_cd, 0x00, sizeof(template_cd));
	memset(msg_body,0x00, sizeof(msg_body));

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
		BEGIN
		proc_get_msg_alimtalk(:telco_id, :msg_id, :talk_id, :dstaddr, :template_cd, :msg_body, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

	switch(ot_sqlcode) {
		case 1:
			sprintf(tmpLog3, "%s:%s:%s:%s", trimR(talk_id).c_str(), trimR(dstaddr).c_str(), trimR(template_cd).c_str(), trimR(msg_body).c_str());
			log3(tmpLog3, 0, 0);
			vtSend.push_back(trimR(talk_id).c_str());
			vtSend.push_back(trimR(dstaddr).c_str());
			vtSend.push_back(trimR(template_cd).c_str());
			vtSend.push_back(trimR(msg_body).c_str());

			return msg_id;
		case 0:
		case -25228:
		case -1405:
			return 0;
		default:
			sprintf(tmpLog3, "CDatabaseORA::getSendData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
			log3(tmpLog3, 0, 0);

			return -1;
	}

	return msg_id;
}

int CDatabaseORA::setSndAckData(sql_context ctx, vector<string>& vtSndAck)
{
	EXEC SQL BEGIN DECLARE SECTION;
	struct sqlca sqlca;
	int nSqlCod, nRetCod, mMsgSeq;
	char sRptCod[2+1], sErrMsg[256];
	EXEC SQL END DECLARE SECTION;

	nSqlCod = nRetCod = mMsgSeq = 0;
	memset(sRptCod, 0x00, sizeof(sRptCod));
	memset(sErrMsg, 0x00, sizeof(sErrMsg));
	vector<string>::iterator itrData;
	itrData = vtSndAck.begin();
	mMsgSeq = atoi(string(*(itrData+3)).c_str());
	strcpy(sRptCod, string(*itrData).c_str());
/*
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_set_ackdata(:mMsgSeq, :sRptCod, :nRetCod, :nSqlCod, :sErrMsg);
	END;
	END-EXEC;
*/
	if (sqlca.sqlcode == -3113 || sqlca.sqlcode == -3114)
		return -1;
	if (sqlca.sqlcode!=0) {
		sprintf(tmpLog3, "CDatabaseORA::setSndAckData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);
		return -1;
	}
	else
		return 1;
}

int CDatabaseORA::setReportData(int quid, char* resCode, sql_context ctx, vector<string>& vtReport, char *Type)
{
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -2;
	char ot_sqlmsg[1024];
	char szRptDate[14+1];
	char endTelco[8];

	struct sqlca sqlca;
	int msgId;
	char szCode[4];
	int telcoID;
	EXEC SQL END DECLARE SECTION;

	memset(szCode,0x00,sizeof szCode);
	memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);
	memset(szRptDate,0x00,sizeof szRptDate);

	memcpy(szCode,resCode,2);
	telcoID = quid;

	vector<string>::iterator itrData;
	itrData = vtReport.begin();

	if (strcmp(Type, "deliver") == 0)
	{
		msgId = atoi(string(*(itrData+2)).c_str());
		strcpy(szRptDate,"19710101000000");
		strcpy(endTelco, "N");
	}
	else if (strcmp(Type, "report") == 0)
	{
		msgId = atoi(string(*itrData).c_str());
		strcpy(szRptDate,string(*(itrData+3)).c_str());
		if (strcmp(szRptDate, "") == 0 || strcmp(szRptDate, "              ") == 0)
		{//통신사로 발송된 시간이 없으면 임의의 값 입력
			strcpy(szRptDate,"19710101000000");
		}
		strcpy(endTelco, string(*(itrData+6)).c_str());
	}

	if (strcmp(endTelco, "KT  ") == 0)
		strcpy(endTelco, "KTF");
	else if (strcmp(endTelco, "LGT ") == 0)
		strcpy(endTelco, "LGT");
	else if (strcmp(endTelco, "SKT ") == 0)
		strcpy(endTelco, "SKT");

	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL EXECUTE
	BEGIN
		proc_telco_res_time(:msgId, :szCode, :telcoID, :endTelco, :szRptDate, :ot_sqlcode, :ot_sqlmsg);
	END;
	END-EXEC;

    if( ot_sqlcode != 0 )
    {
		sprintf(tmpLog3, "CDatabaseORA::setReportData() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);

		if( ot_sqlcode == -1 ) return 0;
        return -1;
    }
return 0;
}

int CDatabaseORA::_putMsgRetryDB(long long msgId, int telcoId, int priority, sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;
    int ot_sqlcode = -1;
    char ot_sqlmsg[1024];
    struct sqlca sqlca;
	//int nMsgID;
	long long nMsgID;
	char cmms_id[30+1];
	int nTelcoID;
	int nPriority;
	EXEC SQL END DECLARE SECTION;

    memset(ot_sqlmsg,0x00,sizeof ot_sqlmsg);
	memset(cmms_id,0x00,sizeof(cmms_id));
	nMsgID = msgId;
	nTelcoID = telcoId;
	nPriority = priority;
	sprintf(cmms_id,"%lld",nMsgID);

#ifdef _URL_MODE
    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL EXECUTE
        BEGIN
        proc_set_urlretry(:cmms_id, :nTelcoID, :nPriority, :ot_sqlcode, :ot_sqlmsg);
    END;
    END-EXEC;
#else
    EXEC SQL CONTEXT USE :ctx;
    EXEC SQL EXECUTE
        BEGIN
        proc_set_msgretry(:cmms_id, :nTelcoID, :nPriority, :ot_sqlcode, :ot_sqlmsg);
    END;
    END-EXEC;
#endif
    if( sqlca.sqlcode != 0 )
    {
		sprintf(tmpLog3, "CDatabaseORA::_putMsgRetryDB() ERROR[%d][%s]", sqlca.sqlcode, sqlca.sqlerrm.sqlerrmc );
		log3(tmpLog3, 0, 0);

        return -1;
    }
    return 0;
}

}

void log3(char *buf, int st, int err)
{
	if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", "DatabaseORA", 0, "");
	}
}
