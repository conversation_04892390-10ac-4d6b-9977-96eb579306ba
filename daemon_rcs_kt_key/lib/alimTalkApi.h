/*
 * AllimtalkApi.h
 *
 * 2015.10.28
 * by <PERSON><PERSON>
 */

#ifndef _ALIMTALKAPI_H_
#define _ALIMTALKAPI_H

#include <iostream>
using namespace std;
#include <string>
#include <vector>
#include <cstdio>
#include "myException.h"
#include "json.h"
#include "base64.h"


typedef struct st_talk_res
{
	string received_at;
	string code;
	string message;
}ST_TALK_RES;

//TOKEN REPONSE 
typedef struct st_token_res
{
	string status;
	string access_token;
	string expires_in;
	string code;
	string message;
}ST_TOKEN_RES;

typedef struct st_token_err
{
	string error;
	string error_description;
}ST_TOKEN_ERR;

//MEDIA REPONSE 
typedef struct st_media_res
{
	string status;
	string code;
	string fileId;
	string expiryDate;
	string message;
}ST_MEDIA_RES;


//MEDIA REPONSE ERR 
typedef struct st_media_err_res
{
	string code;
	string message;
}ST_MEDIA_ERR_RES;

//RCS SEND REPONSE 
typedef struct st_rcs_send_res
{
	string status;
	string code;
	string message;
}ST_RCS_SEND_RES;


typedef struct st_rcs_result_res
{
	string results;
	
	st_rcs_result_res()
	{
		results = "";
	}
	
} ST_RCS_RESULT_RES;

typedef struct st_result_report
{
	string status;
	string result;
	string message;
	string clientMsgId;
	string telecom;
	string date;
	
	st_result_report()
	{
		string status = "";
		string result = "";
		string message = "";
		string clientMsgId = "";
		string telecom = "";
		string date = "";
	}
} ST_RESULT_REPORT;

typedef struct st_talk_polling_res
{
	string code;
	string response_id;
	string responsed_at;
	string message;
	
	st_talk_polling_res()
	{
		code = "";
		response_id = "";
		responsed_at = "";
		message = "";
	}
	
} ST_TALK_POLLING_RES;

typedef struct st_polling_success
{
	string sn;
	string status;
	string received_at;
	
	st_polling_success()
	{
		sn = "";
		status = "";
		received_at = "";
	}
} ST_POLLING_SUCCESS;

typedef struct st_polling_fail
{
	string sn;
	string status;
	string received_at;
	string message;
	
	st_polling_fail()
	{
		sn = "";
		status = "";
		received_at = "";
		message = "";
	}
} ST_POLLING_FAIL;

class CAlimtalkApi
{
public:
	CAlimtalkApi() {};
	~CAlimtalkApi() {};

	void makeAuthData(string username,string clientSec, string &parameter);
	void makeRequestMsg(map<string,string> &_mapSend, char* serviceFlag, string &parameter, long long msgid);
	void makePollingRequestMsg(string channelKey, string &parameter);
	int parsingResponse(string response, ST_TALK_RES &res);
	int parsingTokenResponse(string response, ST_TOKEN_RES &res);
	int parsingTokenErrResponse(string response, ST_TOKEN_ERR &err);
	int parsingMediaResponse(string response, ST_MEDIA_RES &res);
	int parsingMediaErrResponse(string response, ST_MEDIA_ERR_RES &res);
	int parsingSendResponse(string response, ST_RCS_SEND_RES &res);
	int euckrToUtf8(char *source, char *dest, int dest_size);
	int utf8ToEuckr(char *source, char *dest, int dest_size);
	void makeDateString(const string orgDate, string &dateString);
	
	int parsingPollingMsgResponse(string response, ST_TALK_POLLING_RES &res\
		                             , ST_POLLING_SUCCESS * _success, ST_POLLING_FAIL * _fail\
		                             , int & _successSize, int & _failSize);
		                             
    int parsingReportResponse(string response, ST_RCS_RESULT_RES &res\
	                             , ST_RESULT_REPORT * _report\
	                             , int & _reportSize);                             
};

#endif
