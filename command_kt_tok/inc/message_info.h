/*
 * Program Name : message_info.h
 * Comments     : message layout
 * -----------------------------------------------------------------------------
 * History
 *
 *      1) Initial Coding 97.12.23 IM,Song
 */

#ifndef __MESSAGE_INFO_H__
#define __MESSAGE_INFO_H__

#ifdef __cplusplus
extern "C" {
#endif

//#define MSG_MAX_LEN             1024+68 //32bit 
#define MSG_MAX_LEN             1024+76 //64bit
//#define MSG_HEADER_LEN          68 //32bit
#define MSG_HEADER_LEN          76 //64bit

struct _s_buffer
{
    int  message_id;
    int  message_sub_id;
    char process_no[7];
    char process_name[32];
    char select_flag[1];
    long trans_time;
    int  os_process_no;
    int  status_code;
    int  os_error_no;
    int  msg_length;
    char message[MSG_MAX_LEN-MSG_HEADER_LEN];
};

struct _message_info
{
    long mtype;
    union
    {
        char buffer             [MSG_MAX_LEN];
        struct _s_buffer        s_buffer;
    } msg;
};


typedef struct
{
    char    ProcessNo[10];
    int     ProcessCheckFlag;
} _CheckQBody;

typedef struct
{
    long    mesg_type;
    _CheckQBody    body;
} _CheckQ;



#ifdef __cplusplus
}
#endif

#endif /*  __MESSAGE_INFO_H__  */

/* END */
