#ifndef _SENDER_DB_H_
#define _SENDER_DB_H_

#include <iostream> 
#include "stdafx.h"
#include "kssocket.h"
#include "ksthread.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "senderDbInfo.h"
#include "ksconfig.h"

#include <sqlca.h>
#include <queue>
#include <list>
#include <semaphore.h> 
#include <sys/time.h>

using namespace std;

void Init_Oracle(sql_context ctx);

int configParse(char* file);

void* doService(void* param);
void* doDBError(void* param);
int setMsgDB(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int getMMSIDDB(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int getCTNIDDB(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int setMMSTBL(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int setMMSCTNTBL(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int setMMSMSG(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int errorDBprocess(void* pDB);
int getTelcoId(char* szTelco,char* szDstaddr);
int offerInfo(CKSSocket& newSockfd);
int setRPTTBL(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack);
int getMMSSEQDB(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);
int setMMSRPTTBL(void* pDB, struct sqlca sqlca,char* buff,CSenderDbInfoAck& ack);

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];

class CThreadInfo {
    public:
        pthread_t tid;
        int sock; /** domain socket */
};


class CConfigSenderDB {
    public:
        char senderDBName[64];
        int telcoDefaultID;
        int dbFindTimeOut;
        char dbID[16];
        char dbPASS[16];
        char dbSID[16];
};

CConfigSenderDB gConf;



#endif

