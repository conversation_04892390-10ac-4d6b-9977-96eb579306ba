#ifndef _AES256_CBC_H_
#define _AES256_CBC_H_

#include <openssl/conf.h>
#include <openssl/evp.h>
#include <openssl/err.h>
#include <string.h>
#undef BIG_ENDIAN

/* A 256 bit key */
static unsigned char *rawkey = (unsigned char *)"kskyb9466!@#kskyb0955()6649byksk";

/* A 128 bit IV */
static unsigned char *iv = (unsigned char *)"0123456789012345";

int encrypt(unsigned char *plaintext, int plaintext_len, unsigned char *key,
            unsigned char *iv, unsigned char *ciphertext);
            
int decrypt(unsigned char *ciphertext, int ciphertext_len, unsigned char *key,
            unsigned char *iv, unsigned char *plaintext);
       
void handleErrors(void);    


#endif
