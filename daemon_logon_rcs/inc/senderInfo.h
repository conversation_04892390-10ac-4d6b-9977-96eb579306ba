#ifndef _SENDER_INFO_H_
#define _SENDER_INFO_H_

class CSenderInfo {
    public:
        char szSmsTelcoInfo[32];
        char szUrlTelcoInfo[32];
        char szSmsFlag[2];
        char szUrl<PERSON>lag[2];
        char sz<PERSON><PERSON><PERSON><PERSON><PERSON>back[1+1];
		char sz<PERSON><PERSON><PERSON>llowed[5+1];
        char sz<PERSON><PERSON><PERSON><PERSON><PERSON>[1+1];
        char szExpiryOption[1+1];
        char szScheduleType[1+1];
        char szAgencyId[20+1];
        char szBlockYN[1+1];
        char sz<PERSON><PERSON><PERSON>d[13+1];
		char sz<PERSON><PERSON><PERSON><PERSON>[30+1];
		char s<PERSON><PERSON><PERSON><PERSON><PERSON>[30+1];
};

#endif


