#ifndef __CLASSCURL__
#define __CLASSCURL__

#include <iostream>
#include <string>
using namespace std;

#include <curl/curl.h>

//static string response;

static int writer(void *ptr, size_t size, size_t nmemb, void *stream)
{
	((string*)stream)->append((char*)ptr, size*nmemb);
	return size*nmemb;
	
	//return 0;
}

class CCurl
{
public:
	CCurl() {}
	~CCurl() {}
	
	void init();
	void setOptPost(char *targetUrl, string parameter);
	void setHeaderPost(char *string);
	void cleanAll();

	CURLcode perform();
	CURLcode getContentInfo(char *contenInfo);
	//int writer(char *data, size_t size, size_t nmemb, char *buffer_in);

	CURL *handle;
	string response;
	curl_slist *headers;
};
#endif

