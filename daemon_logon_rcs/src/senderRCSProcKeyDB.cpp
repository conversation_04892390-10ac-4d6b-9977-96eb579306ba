#include "senderMMSProcessDB.h"
#include <sys/time.h>
#include <stdlib.h>
#include <wchar.h>
#include <map>
//#include <time.h>
static CMMSPacketSend mmsPacketSend;
static int S_PROCESS_NO;
// 20180313 MMSID SEQ USE
static int proc_id;
char szUid[42]; //복호화된 DB 접속정보
char szBcMBaseId[40+1];
//char szBcCallBack[16+1];

map<string, string> mapBrandKey;

int main(int argc,char* argv[])
{
	
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version
	 * 4 : conf file
	 */
	int sockfd;
	int fd;
	int ret;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	
	sockfd = atoi(argv[1]);
	fd = atoi(argv[2]);
	
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
	
	memset(_DATALOG,0x00,sizeof(_DATALOG));//CCL(_DATALOG);
	memset(_MONILOG,0x00,sizeof(_MONILOG));//CCL(_MONILOG);
	char* p;
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szSenderID,0x00,sizeof(szSenderID));//CCL(szSenderID);
	strcpy(szSenderID,logonDbInfo.szCID);
	S_PROCESS_NO = getpid();
	
	p = strtok(logonDbInfo.szLogFilePath,"|");
	if( p )
	{
		strcat(_MONILOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{
		strcat(_DATALOG,p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	logPrintS(0,"[INF] filepath - logfile[%s] monitorfile[%s] PID[%d]",_DATALOG,_MONILOG, S_PROCESS_NO);
		
	ret = configParse(argv[4]);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] configParse Failed");
	    exit(1);
	}
	
	logPrintS(0,"[INF] config file - logonDBName [%s]",gConf.logonDBName);
	
	if(decPassword()<0){
		logPrintS(0,"[ERR] Password decryption Error");	
		return -1;
	}
	
	
	//if (g_oracle.connectToOracle(gConf.dbuid, gConf.dbdsn)<0)
	if (g_oracle.connectToOracle(szUid, gConf.dbdsn)<0)
	{
		logPrintS(0,"[ERR] connectToOracle Failed");
	    return -1;
	}
	
	logPrintS(1,"[INF] ORACLE CONNECT");
	
	proc_id = g_oracle.selectSEQ();
	
	if( proc_id == -1 )
		proc_id = 9999;
	
	SenderProcess *mSenderProcess = new SenderProcess();

	// sendre Main Process Start 
	mSenderProcess->SenderMain(sockfd,logonDbInfo);
	
	if (g_oracle.closeFromOracle()<0)
	{
		logPrintS(0,"[ERR] closeFromOracle Failed");
	    return -1;
	}
	logPrintS(1,"[INF] ORACLE DISCONNECT");
	
	return 0;
}


int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc)
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

	memset(szCode		,0x00	,sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
		
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] socket ack ctnid[%d]key[%s]code[%s]strDesc[%s]", ctnid, key.c_str(), szCode, strDesc.c_str() );

	return ret;
}

int sendCtnAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc) 
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

	memset(szCode		,0x00	,sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN CTNACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
		
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] socket ctn ack ctnid[%d]key[%s]code[%s]strDesc[%s]", ctnid, key.c_str(), szCode, strDesc.c_str() );

	return ret;
}

int sendPong(CKSSocket& hRemoteSock)
{
	string strPacket;
	string strKey;
	CMMSPacketBase packetBase;
	int ret=0;
	
	packetBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);

	strPacket = "BEGIN PONG\r\nKEY:" + strKey + "\r\nEND\r\n";
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintS(0,"[ERR] socket ack send failed sendSize/packetSize[%d/%d]",ret,strPacket.length());
		return ret;
	}
	//logPrintS(0,"[INF] socket link PONG send");
  
	fflush(stdout);
	return 0;
}

/** @return 음수 일시 프로세스 종료 */
int recvLink(CKSSocket& hRemoteSock,char* buff)
{
	int ret;

	TypeMsgDataAck* pLinkAck = (TypeMsgDataAck*)buff;
	memset(pLinkAck->header.msgType, 0x00, sizeof(pLinkAck->header.msgType));
	strcpy(pLinkAck->header.msgType,"8");

	ret = hRemoteSock.send(buff,sizeof(TypeMsgDataAck));
	if( ret != sizeof(TypeMsgDataAck))
	{
		logPrintS(0,"[ERR] socket link ack send failed - errno[%s] sendSize/packetSize[%d/%d]", strerror(errno), ret, sizeof(TypeMsgDataAck));
		return -1;
	}
	time(&SLastTLink);

	return 0;
}

void logPrintS(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[S][%s] %s",szSenderID,tmpMsg);
	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;
	
	memset(szBcMBaseId,0x00,sizeof(szBcMBaseId));
	//memset(szBcCallBack,0x00,sizeof(szBcCallBack));
	
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	
	conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
	
	if( gConf.monitorName == NULL )
	{
		strcpy(gConf.monitorName,"");
	}

	conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
	if( gConf.domainPath == NULL )
	{
		strcpy(gConf.domainPath,"");
	}

	conf.strncpy2(gConf.ContentPath , conf.FetchEntry("path.mmscontent"),64);
	
	if( gConf.ContentPath == NULL )
	{
		strcpy(gConf.ContentPath,"");
	}

	gConf.socketLinkTimeOut = conf.FetchEntryInt("socket.linktimeout");
	
	if( gConf.socketLinkTimeOut <= 1 )
	{
		gConf.socketLinkTimeOut = 2;
	}

	gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");

	if( gConf.dbRequestTimeOut <= 0 )
	{
		gConf.dbRequestTimeOut = 1;
	}
	
	conf.strncpy2(gConf.dbuid , conf.FetchEntry("db.uid"),64);
	
	if( gConf.dbuid == NULL )
	{
		strcpy(gConf.dbuid,"");
	}

	conf.strncpy2(gConf.dbdsn , conf.FetchEntry("db.dsn"),64);
	
	if( gConf.dbdsn == NULL )
	{
		strcpy(gConf.dbuid,"");
	}

#if (DEBUG >= 5)
	logPrintS(0,"[DEB] bc.mbasid [%p] [%s]", conf.FetchEntry("bc.mbasid"), conf.FetchEntry("bc.mbasid"));
#endif

	char *pMbaseId = conf.FetchEntry("bc.mbasid");
	if (pMbaseId != NULL) {
		strncpy(szBcMBaseId , conf.FetchEntry("bc.mbasid"),sizeof(szBcMBaseId)-1);
	}

#if (DEBUG >= 5)
	logPrintS(0,"[DEB] szBcMBaseId [%p] [%s]", szBcMBaseId, szBcMBaseId);
#endif
	
	if( szBcMBaseId == NULL )
	{
		strcpy(szBcMBaseId,"");
	}
	
	/*strncpy(szBcCallBack , conf.FetchEntry("bc.callback"),sizeof(szBcCallBack)-1);
	
	if( szBcCallBack == NULL )
	{
		strcpy(szBcCallBack,"");
	}*/
	
	return 0;
}


void viewPackSender(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];
	
	memset(logMsg,0x00, sizeof logMsg);
	memset(strtmp,0x00, sizeof strtmp);
	
	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
	    }
	    else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}
	
	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}

int setMMSCTNMSG2DB(char* szCid, int ctnid, CMMSFileProcess& mmsFileProcess,int priority,CBcastData * pBcastData)
{
	int ret, size;
	int nTelcoFlag = 0;
    //char szType[50+1];
    
	CSenderDbCTNMMSMSG senderDbCTNMMSMSG;
	
	CMMSCtnTbl mmsCtnMsg;
	
	ret = mmsFileProcess.getMMSCtnTblFirst(mmsCtnMsg);

    nTelcoFlag =4; //USE CONTENT TELCO ID
    
    
	memset(&senderDbCTNMMSMSG,0x00,sizeof(CSenderDbCTNMMSMSG));
	sprintf(senderDbCTNMMSMSG.szQName,"%d", getTelcoId(gSenderInfo.szSmsTelcoInfo, nTelcoFlag));
	
	senderDbCTNMMSMSG.nPriority = priority;
	senderDbCTNMMSMSG.nCtnId = ctnid;

	memcpy(senderDbCTNMMSMSG.szCtnName, mmsCtnMsg.szCtnName, sizeof(mmsCtnMsg.szCtnName));
	memcpy(senderDbCTNMMSMSG.szBrandId, gSenderInfo.szBrandId, sizeof(senderDbCTNMMSMSG.szBrandId));
	
	ret = g_oracle.setMMSCTNMSG(senderDbCTNMMSMSG);
	if (ret <= 0)
	{
		logPrintS(1,"[ERR] setMMSCTNMSG2DB [%d]", ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
	
}


int setMMSCTNTBL2DB(CMMSFileProcess& mmsFileProcess,CMMSPacketSend& mmsPacketSend)
{
	int ret;
	CSenderDbMMSCTNTBL senderDbMMSCTNTBL;
	
	CMMSCtnTbl mmsCtnTbl;
	
	ret = mmsFileProcess.getMMSCtnTblFirst(mmsCtnTbl);
	if( ret != 0 ) return 1; 
	
	while(ret == 0 )
	{
		memset(&senderDbMMSCTNTBL,0x00,sizeof(CSenderDbMMSCTNTBL));	
		senderDbMMSCTNTBL.nCtnId = mmsCtnTbl.nCtnId;
		memcpy(senderDbMMSCTNTBL.szCtnName, mmsCtnTbl.szCtnName, sizeof(mmsCtnTbl.szCtnName));
		memcpy(senderDbMMSCTNTBL.szCtnMime, mmsCtnTbl.szCtnMime, sizeof(mmsCtnTbl.szCtnMime));
		senderDbMMSCTNTBL.nCtnSeq = mmsCtnTbl.nCtnSeq;
		memcpy(senderDbMMSCTNTBL.szCtnSvc , mmsCtnTbl.szCtnSvc , sizeof(mmsCtnTbl.szCtnSvc ));
		memcpy(senderDbMMSCTNTBL.szPtnSn , mmsPacketSend.getKeyValue() , sizeof(senderDbMMSCTNTBL.szPtnSn)-1);
		memcpy(senderDbMMSCTNTBL.szCid , szSenderID , sizeof(senderDbMMSCTNTBL.szCid));
		senderDbMMSCTNTBL.nSize = mmsCtnTbl.nSize;
		
		ret = g_oracle.setMMSCTNTBL(senderDbMMSCTNTBL);
		if (ret <= 0)
		{
			logPrintS(1,"[ERR] setMMSCTNTBL2DB ctnid[%d] ret[%d]", ret);
		}
		ret = mmsFileProcess.getMMSCtnTblNext(mmsCtnTbl);
	}

	return 0; // OK

}

int setMMSMSG2DB(char* szCid, long long nMMSId,int ctnid, CMMSFileProcess& mmsFileProcess,int priority,int bcFlag,CBcastData * pBcastData)
{
	int ret, size;
	int nTelcoFlag = 0;
    //char szType[50+1];
    char szMBaseId[40+1];
    char szMsgBody[2600+1];
    char szMsgBody2[120+1];
    char szMsgBody3[120+1];
    char szMsgBody4[120+1];
    char szMsgBody5[120+1];
    char szMsgBody6[120+1];
    char* pData;
    CMData mData;
	CSenderDbMMSMSG senderDbMMSMSG;
	ST_TXT_RES res;
	string strBrandKey;
	
		
    /*ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	memset(szType, 0x00, sizeof(szType));
    sprintf(szType,(char*)mData.contentType.strType.c_str());
    if (strcmp(szType, "TXT") == 0)
    {
    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    }
    else
    {
    	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	    {
	        memset(szType,0x00,sizeof(szType));	
	        sprintf(szType,(char*)mData.contentType.strType.c_str());
		    if (strcmp(szType, "TXT") == 0)
		    {
		    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
		    	break;
		    }
	    }
    }*/
    
    memset(szMBaseId,0x00,sizeof(szMBaseId));	
    memset(szMsgBody,0x00,sizeof(szMsgBody));	
    memset(szMsgBody2,0x00,sizeof(szMsgBody2));	
    memset(szMsgBody3,0x00,sizeof(szMsgBody3));	
    memset(szMsgBody4,0x00,sizeof(szMsgBody4));	
    memset(szMsgBody5,0x00,sizeof(szMsgBody5));	
    memset(szMsgBody6,0x00,sizeof(szMsgBody6));	
    
    /*if(bcFlag == 1)
    {
    	strcpy(szMBaseId,mmsPacketSend.getSenderKeyValue());
    	trim(szMBaseId,strlen(szMBaseId));
    }
    else
    {
    	strcpy(szMBaseId,mmsPacketSend.getMBaseIdValue());
    	trim(szMBaseId,strlen(szMBaseId));
    }*/
    strcpy(szMBaseId,mmsPacketSend.getMBaseIdValue());
    trim(szMBaseId,strlen(szMBaseId));

	if(strncmp(szMBaseId,"UBR", 3) == 0) {
		nTelcoFlag =1;
	}
    else if(strcmp(szMBaseId,"SS000000") == 0)
    {
    	nTelcoFlag =1;	
    }
    //else if(strcmp(szMBaseId,"SL000000") == 0 ||
	else if(strncmp(szMBaseId,"SL", 2) == 0 ||	
		     strncmp(szMBaseId,"LBR", 3) == 0 || strncmp(szMBaseId,"OL", 2) == 0)
    {
		nTelcoFlag =2;
    }
//    else if(strcmp(szMBaseId,"SMwThT00") == 0||strcmp(szMBaseId,"SMwThM00") == 0||
//    		strcmp(szMBaseId,"SMwLhX00") == 0||strcmp(szMBaseId,"SMwRhX00") == 0||
//    		strcmp(szMBaseId,"CMwMhM0300") == 0||strcmp(szMBaseId,"CMwMhM0400") == 0||
//    		strcmp(szMBaseId,"CMwMhM0500") == 0||strcmp(szMBaseId,"CMwMhM0600") == 0||
//    		strcmp(szMBaseId,"CMwShS0300") == 0||strcmp(szMBaseId,"CMwShS0400") == 0||
//    		strcmp(szMBaseId,"CMwShS0500") == 0||strcmp(szMBaseId,"CMwShS0600") == 0)
    else if (strncmp(szMBaseId, "SM", 2) == 0 || strncmp(szMBaseId, "CM", 2) == 0 ||
			  strncmp(szMBaseId, "OM", 2) == 0)
	{
    	nTelcoFlag =3;
    }
	else if (strncmp(szMBaseId,"IBR", 3) == 0) {
		nTelcoFlag =1;
	}
    else{
    	nTelcoFlag =1;	//template
    }

    memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG));    
    
    if (0 == strncmp(szMBaseId, "IBR", 3) )
    {
    	//Image Template telco - 5
    	sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(gSenderInfo.szSmsTelcoInfo, 5));
    	
		logPrintS(1, "Image Template Telco [%s]", senderDbMMSMSG.szQName);
    }
    else
    {        
		sprintf(senderDbMMSMSG.szQName,"%d", getTelcoId(gSenderInfo.szSmsTelcoInfo, nTelcoFlag));
    }
	
	senderDbMMSMSG.nPriority = priority;
	senderDbMMSMSG.nCtnId = ctnid;
	senderDbMMSMSG.nTelcoFlag = nTelcoFlag;
	
	//strcpy(senderDbMMSMSG.szMBaseId,mmsPacketSend.getMBaseIdValue());
	strcpy(senderDbMMSMSG.szMBaseId,szMBaseId);
	strcpy(senderDbMMSMSG.szGroupId,mmsPacketSend.getGroupIdValue());
	strcpy(senderDbMMSMSG.szDstAddr,mmsPacketSend.getReceiverValue());
	
	/*if(bcFlag == 1)
    {
    	strcpy(senderDbMMSMSG.szCallBack,szBcCallBack);
    }
    else
    {*/
		strcpy(senderDbMMSMSG.szCallBack,mmsPacketSend.getSenderValue());
	//}
	
	senderDbMMSMSG.nCntType = mmsPacketSend.getCtnType();
	
	strcpy(senderDbMMSMSG.szMsgTitle1,mmsPacketSend.getMsgTitle1Value());
	strcpy(senderDbMMSMSG.szMsgTitle2,mmsPacketSend.getMsgTitle2Value());
	strcpy(senderDbMMSMSG.szMsgTitle3,mmsPacketSend.getMsgTitle3Value());
	strcpy(senderDbMMSMSG.szMsgTitle4,mmsPacketSend.getMsgTitle4Value());
	strcpy(senderDbMMSMSG.szMsgTitle5,mmsPacketSend.getMsgTitle5Value());
	strcpy(senderDbMMSMSG.szMsgTitle6,mmsPacketSend.getMsgTitle6Value());
	
	strncpy(szMsgBody,mmsPacketSend.getMsgBodyValue(),sizeof(szMsgBody)-1);
	strncpy(szMsgBody2,mmsPacketSend.getMsgBody2Value(),sizeof(szMsgBody2)-1);
	strncpy(szMsgBody3,mmsPacketSend.getMsgBody3Value(),sizeof(szMsgBody3)-1);
	strncpy(szMsgBody4,mmsPacketSend.getMsgBody4Value(),sizeof(szMsgBody4)-1);
	strncpy(szMsgBody5,mmsPacketSend.getMsgBody5Value(),sizeof(szMsgBody5)-1);
	strncpy(szMsgBody6,mmsPacketSend.getMsgBody6Value(),sizeof(szMsgBody6)-1);

	trim(szMsgBody,strlen(szMsgBody));
	trim(szMsgBody2,strlen(szMsgBody2));
	trim(szMsgBody3,strlen(szMsgBody3));
	trim(szMsgBody4,strlen(szMsgBody4));
	trim(szMsgBody5,strlen(szMsgBody5));
	trim(szMsgBody6,strlen(szMsgBody6));
	
	if(strlen(szMsgBody) > 0)
	{
		if(strcmp(szMBaseId,"CMwMhM0300") == 0 || strcmp(szMBaseId,"CMwShS0300") == 0)
		{
			strcpy(senderDbMMSMSG.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSMSG.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSMSG.szMsgBody3,szMsgBody3);
			
			
		}
		else if(strcmp(szMBaseId,"CMwMhM0400") == 0 || strcmp(szMBaseId,"CMwShS0400") == 0)
		{
			strcpy(senderDbMMSMSG.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSMSG.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSMSG.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSMSG.szMsgBody4,szMsgBody4);
		}
		else if(strcmp(szMBaseId,"CMwMhM0500") == 0 || strcmp(szMBaseId,"CMwShS0500") == 0)
		{
			strcpy(senderDbMMSMSG.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSMSG.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSMSG.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSMSG.szMsgBody4,szMsgBody4);
			strcpy(senderDbMMSMSG.szMsgBody5,szMsgBody5);
		}
		else if(strcmp(szMBaseId,"CMwMhM0600") == 0 || strcmp(szMBaseId,"CMwShS0600") == 0)
		{	
			strcpy(senderDbMMSMSG.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSMSG.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSMSG.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSMSG.szMsgBody4,szMsgBody4);
			strcpy(senderDbMMSMSG.szMsgBody5,szMsgBody5);
			strcpy(senderDbMMSMSG.szMsgBody6,szMsgBody6);
		}
		else
		{
			//strcpy(senderDbMMSMSG.szMsgBody1,mmsPacketSend.getMsgBodyValue());
			strcpy(senderDbMMSMSG.szMsgBody1,szMsgBody);
		}
	}
	
	strcpy(senderDbMMSMSG.szFileId1,mmsPacketSend.getFileId1Value());
	strcpy(senderDbMMSMSG.szFileId2,mmsPacketSend.getFileId2Value());
	strcpy(senderDbMMSMSG.szFileId3,mmsPacketSend.getFileId3Value());
	strcpy(senderDbMMSMSG.szFileId4,mmsPacketSend.getFileId4Value());
	strcpy(senderDbMMSMSG.szFileId5,mmsPacketSend.getFileId5Value());
	strcpy(senderDbMMSMSG.szFileId6,mmsPacketSend.getFileId6Value());
	
	strcpy(senderDbMMSMSG.szButton1,mmsPacketSend.getButton1Value());
	strcpy(senderDbMMSMSG.szButton2,mmsPacketSend.getButton2Value());
	strcpy(senderDbMMSMSG.szButton3,mmsPacketSend.getButton3Value());
	strcpy(senderDbMMSMSG.szButton4,mmsPacketSend.getButton4Value());
	strcpy(senderDbMMSMSG.szButton5,mmsPacketSend.getButton5Value());
	strcpy(senderDbMMSMSG.szButton6,mmsPacketSend.getButton6Value());
	strcpy(senderDbMMSMSG.szButton7,mmsPacketSend.getButton7Value());
	strcpy(senderDbMMSMSG.szButton8,mmsPacketSend.getButton8Value());
	strcpy(senderDbMMSMSG.szButton9,mmsPacketSend.getButton9Value());
	strcpy(senderDbMMSMSG.szButton10,mmsPacketSend.getButton10Value());
	strcpy(senderDbMMSMSG.szButton11,mmsPacketSend.getButton11Value());
	strcpy(senderDbMMSMSG.szButton12,mmsPacketSend.getButton12Value());
	
	if (bcFlag == 1)
	{
		if(strlen(mmsPacketSend.getHeadValue()) > 0)
		{
			strcpy(senderDbMMSMSG.szHead,mmsPacketSend.getHeadValue());
		}
		else
		{
			strcpy(senderDbMMSMSG.szHead,"0");	
		}
	}
	else
	{
		strcpy(senderDbMMSMSG.szHead,mmsPacketSend.getHeadValue());	
	}
	
	//strcpy(senderDbMMSMSG.szHead,mmsPacketSend.getHeadValue());
	strcpy(senderDbMMSMSG.szFooter,mmsPacketSend.getFooterValue());
	strcpy(senderDbMMSMSG.szCopyAllowed,gSenderInfo.szCopyAllowed);
	strcpy(senderDbMMSMSG.szRcsConvert,gSenderInfo.szRcsConvert);
	strcpy(senderDbMMSMSG.szExpiryOption,gSenderInfo.szExpiryOption);
	strcpy(senderDbMMSMSG.szScheduleType,gSenderInfo.szScheduleType);
	strcpy(senderDbMMSMSG.szAgencyId,gSenderInfo.szAgencyId);
	
	//20230322 add	
#if (DEBUG >= 5)
	logPrintS(1,"[DBG] mmsPacketSend.getAgencyKeyValue()  [%s] ", mmsPacketSend.getAgencyKeyValue() );    
#endif
	if (strcmp(mmsPacketSend.getAgencyKeyValue(), "") == 0)
	{
		// neorcs.ptn_info - agency_key
		strcpy(senderDbMMSMSG.szAgencyKey, gSenderInfo.szAgencyKey);		
	}
	else
	{
		// received agency_key
		strcpy(senderDbMMSMSG.szAgencyKey, mmsPacketSend.getAgencyKeyValue());		
	}
	
	//strcpy(senderDbMMSMSG.szBrandKey,gSenderInfo.szBrandKey);
#if (DEBUG >= 5)
	logPrintS(1,"[DBG] mmsPacketSend.getBrandKeyValue()  [%s] ", mmsPacketSend.getBrandKeyValue() );    
#endif

	if (strcmp(mmsPacketSend.getBrandKeyValue(), "") == 0)
	{
		// brand_key search DB : CallBack -> ChatbotId
		ret = g_oracle.selectBrandKey(senderDbMMSMSG.szCallBack, strBrandKey);
		if( ret <= 0 )
		{
			logPrintS(1,"[ERR] selectBrandKey failed [%d] CallBack [%s]", ret, senderDbMMSMSG.szCallBack);    
			return -1;
		}
		else
		{
#if (DEBUG >= 4)
			logPrintS(1,"[DBG] selectBrandKey CallBack [%s] brandKey [%s] ", senderDbMMSMSG.szCallBack, strBrandKey.c_str());    
#endif
			strcpy(senderDbMMSMSG.szBrandKey, strBrandKey.c_str());
		}		
	}
	else
	{
		// received brand_key
		strcpy(senderDbMMSMSG.szBrandKey, mmsPacketSend.getBrandKeyValue());
	}
	
	sprintf(senderDbMMSMSG.szRepFlag,"%s",mmsPacketSend.getRepFlagValue());
    
    if(strcmp(senderDbMMSMSG.szRepFlag,"S") != 0 && 
    	strcmp(senderDbMMSMSG.szRepFlag,"L") != 0 &&
    	strcmp(senderDbMMSMSG.szRepFlag,"M") != 0)
    {
    	sprintf(senderDbMMSMSG.szRepFlag,"N");
    }
	
	//strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		
	senderDbMMSMSG.nRgnRate = 65;
	senderDbMMSMSG.nInterval = 10;
	senderDbMMSMSG.nMMSId = nMMSId;

	ret = g_oracle.setMMSMSG(senderDbMMSMSG);
	if (ret <= 0)
	{
		logPrintS(1,"[ERR] setMMSMSG mmsid[%lld] ret[%d]", nMMSId, ret);
		return ret;
	}
	else
	{
		ret = 100;	
		return ret;
	}		
	
}

int setMMSTBL2DB(long long nMMSId, int ctnid, CMMSFileProcess& mmsFileProcess,int priority,int bcFlag,CBcastData * pBcastData)
{
	int ret;
	int nTelcoFlag = 0;
	char szMBaseId[40+1];
	char szMsgBody[2600+1];
	char szMsgBody2[120+1];
    char szMsgBody3[120+1];
    char szMsgBody4[120+1];
    char szMsgBody5[120+1];
    char szMsgBody6[120+1];
	char szReplaceMsgBody[2000+1];
	CSenderDbMMSTBL senderDbMMSTBL;
	ST_TXT_RES res;
	
	CMMSRepCtnTbl mmsRepCtnTbl;
	
	memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));

	memset(szMBaseId,0x00,sizeof(szMBaseId));	
    memset(szMsgBody,0x00,sizeof(szMsgBody));	
    memset(szMsgBody2,0x00,sizeof(szMsgBody2));	
    memset(szMsgBody3,0x00,sizeof(szMsgBody3));	
    memset(szMsgBody4,0x00,sizeof(szMsgBody4));	
    memset(szMsgBody5,0x00,sizeof(szMsgBody5));	
    memset(szMsgBody6,0x00,sizeof(szMsgBody6));	
    memset(szReplaceMsgBody,0x00,sizeof(szReplaceMsgBody));
    
    /*if(bcFlag == 1)
    {
    	strncpy(szMBaseId,mmsPacketSend.getSenderKeyValue(),sizeof(szMBaseId)-1);
    	trim(szMBaseId,strlen(szMBaseId));
    }
    else
    {
    	strncpy(szMBaseId,mmsPacketSend.getMBaseIdValue(),sizeof(szMBaseId)-1);
    	trim(szMBaseId,strlen(szMBaseId));
    }*/
    
    strncpy(szMBaseId,mmsPacketSend.getMBaseIdValue(),sizeof(szMBaseId)-1);
    trim(szMBaseId,strlen(szMBaseId));

	if(strncmp(szMBaseId,"UBR", 3) == 0) {
		nTelcoFlag =1;
	}
    else if(strcmp(szMBaseId,"SS000000") == 0)
    {
    	nTelcoFlag =1;	
    }
    //else if(strcmp(szMBaseId,"SL000000") == 0)
	else if(strncmp(szMBaseId,"SL", 2) == 0 ||	
	     strncmp(szMBaseId,"LBR", 3) == 0 || strncmp(szMBaseId,"OL", 2) == 0)
    {
		nTelcoFlag =2;	
    }
	else if(strncmp(szMBaseId,"LBR", 3) == 0)	// LMS 템플릿
    {	
		nTelcoFlag =2;	
    }
    else if (strncmp(szMBaseId, "SM", 2) == 0 || strncmp(szMBaseId, "CM", 2) == 0 ||
			  strncmp(szMBaseId, "OM", 2) == 0)    		
    {
    	nTelcoFlag =3;	
    }
	else if (strncmp(szMBaseId,"IBR", 3) == 0) {
		nTelcoFlag =1;
	}
    else{
    	nTelcoFlag =1;	//template
    }
    
    sprintf(senderDbMMSTBL.szRepFlag,"%s",mmsPacketSend.getRepFlagValue());
    
    if(strcmp(senderDbMMSTBL.szRepFlag,"S") != 0 && 
    	strcmp(senderDbMMSTBL.szRepFlag,"L") != 0 &&
    	strcmp(senderDbMMSTBL.szRepFlag,"M") != 0)
    {
    	sprintf(senderDbMMSTBL.szRepFlag,"N");
    }
	strncpy(senderDbMMSTBL.szRepMsgTitle,mmsPacketSend.getRepMsgTitleValue(),64);
	
	strcpy(senderDbMMSTBL.szRepMsgBody,mmsPacketSend.getRepMsgBodyValue());
	
	strcpy(szReplaceMsgBody,mmsPacketSend.getRepMsgBodyValue());
	trim(szReplaceMsgBody,strlen(szReplaceMsgBody));
	
	if(strcmp(mmsPacketSend.getRepFlagValue(),"S")==0)
	{
		if(strlen(szReplaceMsgBody) > 90)
		{
			//logPrintS(0, "[ERR] setMMSTBL2DB replace message over 90byte mmsid[%lld][%s]",nMMSId,szMBaseId);
			return -3;
		}
	}
	else if(strcmp(mmsPacketSend.getRepFlagValue(),"L")==0||strcmp(mmsPacketSend.getRepFlagValue(),"M")==0)
	{
		if(strlen(szReplaceMsgBody) > 2000)
		{
			//logPrintS(0, "[ERR] setMMSTBL2DB replace message over 2000byte mmsid[%lld][%s]",nMMSId,szMBaseId);
			return -3;
		}
	}
	
	
	if(strcmp(mmsPacketSend.getRepFlagValue(),"M")==0)
	{
		ret = mmsFileProcess.getMMSRepCtnTblFirst(mmsRepCtnTbl);
		if( ret != 0 ) return -2; 
		
		while(ret == 0 )
		{
			if(mmsRepCtnTbl.nCtnSeq ==1)
			{
				strcpy(senderDbMMSTBL.szRepCtnName1,mmsRepCtnTbl.szCtnName1);
			}
			else if(mmsRepCtnTbl.nCtnSeq ==2)
			{
				strcpy(senderDbMMSTBL.szRepCtnName2,mmsRepCtnTbl.szCtnName2);
			}
			else if(mmsRepCtnTbl.nCtnSeq ==3)
			{
				strcpy(senderDbMMSTBL.szRepCtnName3,mmsRepCtnTbl.szCtnName3);
			}
			ret = mmsFileProcess.getMMSRepCtnTblNext(mmsRepCtnTbl);
		}
    }
    
	//strncpy(senderDbMMSTBL.szMBaseId,mmsPacketSend.getMBaseIdValue(),sizeof(senderDbMMSTBL.szMBaseId)-1);
	
	strncpy(senderDbMMSTBL.szMBaseId,szMBaseId,sizeof(senderDbMMSTBL.szMBaseId)-1);
	
	strncpy(senderDbMMSTBL.szGroupId,mmsPacketSend.getGroupIdValue(),sizeof(senderDbMMSTBL.szGroupId)-1);
	
	/*if(bcFlag == 1)
    {
    	strncpy(senderDbMMSTBL.szCallBack,szBcCallBack,sizeof(senderDbMMSTBL.szCallBack)-1);
    }
    else
    {*/
		strncpy(senderDbMMSTBL.szCallBack,mmsPacketSend.getSenderValue(),sizeof(senderDbMMSTBL.szCallBack)-1);
	//}
	strncpy(senderDbMMSTBL.szDstAddr,mmsPacketSend.getReceiverValue(),sizeof(senderDbMMSTBL.szDstAddr)-1	);
	
	strncpy(senderDbMMSTBL.szMsgTitle1,mmsPacketSend.getMsgTitle1Value(),sizeof(senderDbMMSTBL.szMsgTitle1)-1);
	strncpy(senderDbMMSTBL.szMsgTitle2,mmsPacketSend.getMsgTitle2Value(),sizeof(senderDbMMSTBL.szMsgTitle2)-1);
	strncpy(senderDbMMSTBL.szMsgTitle3,mmsPacketSend.getMsgTitle3Value(),sizeof(senderDbMMSTBL.szMsgTitle3)-1);
	strncpy(senderDbMMSTBL.szMsgTitle4,mmsPacketSend.getMsgTitle4Value(),sizeof(senderDbMMSTBL.szMsgTitle4)-1);
	strncpy(senderDbMMSTBL.szMsgTitle5,mmsPacketSend.getMsgTitle5Value(),sizeof(senderDbMMSTBL.szMsgTitle5)-1);
	strncpy(senderDbMMSTBL.szMsgTitle6,mmsPacketSend.getMsgTitle6Value(),sizeof(senderDbMMSTBL.szMsgTitle6)-1);

	if (nTelcoFlag == 1) {
		strncpy(szMsgBody,mmsPacketSend.getMsgBodyValue(),sizeof(szMsgBody)-1);
		trim(szMsgBody,strlen(szMsgBody));
	}

	/**
	strncpy(szMsgBody2,mmsPacketSend.getMsgBody2Value(),sizeof(szMsgBody2)-1);
	trim(szMsgBody2,strlen(szMsgBody2));
	
	strncpy(szMsgBody3,mmsPacketSend.getMsgBody3Value(),sizeof(szMsgBody3)-1);
	trim(szMsgBody3,strlen(szMsgBody3));
	
	strncpy(szMsgBody4,mmsPacketSend.getMsgBody4Value(),sizeof(szMsgBody4)-1);
	trim(szMsgBody4,strlen(szMsgBody4));
	
	strncpy(szMsgBody5,mmsPacketSend.getMsgBody5Value(),sizeof(szMsgBody5)-1);
	trim(szMsgBody5,strlen(szMsgBody5));
	
	strncpy(szMsgBody6,mmsPacketSend.getMsgBody6Value(),sizeof(szMsgBody6)-1);
	trim(szMsgBody6,strlen(szMsgBody6));
	**/
	
	if(strlen(szMsgBody) > 0)
	{
		if(strcmp(szMBaseId,"CMwMhM0300") == 0 || strcmp(szMBaseId,"CMwShS0300") == 0)
		{
			strcpy(senderDbMMSTBL.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSTBL.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSTBL.szMsgBody3,szMsgBody3);
		}
		else if(strcmp(szMBaseId,"CMwMhM0400") == 0 || strcmp(szMBaseId,"CMwShS0400") == 0)
		{
			strcpy(senderDbMMSTBL.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSTBL.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSTBL.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSTBL.szMsgBody4,szMsgBody4);
		}
		else if(strcmp(szMBaseId,"CMwMhM0500") == 0 || strcmp(szMBaseId,"CMwShS0500") == 0)
		{
			strcpy(senderDbMMSTBL.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSTBL.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSTBL.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSTBL.szMsgBody4,szMsgBody4);
			strcpy(senderDbMMSTBL.szMsgBody5,szMsgBody5);
		}
		else if(strcmp(szMBaseId,"CMwMhM0600") == 0 || strcmp(szMBaseId,"CMwShS0600") == 0)
		{	
			strcpy(senderDbMMSTBL.szMsgBody1,szMsgBody);
			strcpy(senderDbMMSTBL.szMsgBody2,szMsgBody2);
			strcpy(senderDbMMSTBL.szMsgBody3,szMsgBody3);
			strcpy(senderDbMMSTBL.szMsgBody4,szMsgBody4);
			strcpy(senderDbMMSTBL.szMsgBody5,szMsgBody5);
			strcpy(senderDbMMSTBL.szMsgBody6,szMsgBody6);
		}
		else
		{
			//strcpy(senderDbMMSTBL.szMsgBody1,mmsPacketSend.getMsgBodyValue());
			strcpy(senderDbMMSTBL.szMsgBody1,szMsgBody);
		}
	}
	
	strncpy(senderDbMMSTBL.szFileId1,mmsPacketSend.getFileId1Value(),sizeof(senderDbMMSTBL.szFileId1)-1);
	strncpy(senderDbMMSTBL.szFileId2,mmsPacketSend.getFileId2Value(),sizeof(senderDbMMSTBL.szFileId2)-1);
	strncpy(senderDbMMSTBL.szFileId3,mmsPacketSend.getFileId3Value(),sizeof(senderDbMMSTBL.szFileId3)-1);
	strncpy(senderDbMMSTBL.szFileId4,mmsPacketSend.getFileId4Value(),sizeof(senderDbMMSTBL.szFileId4)-1);
	strncpy(senderDbMMSTBL.szFileId5,mmsPacketSend.getFileId5Value(),sizeof(senderDbMMSTBL.szFileId5)-1);
	strncpy(senderDbMMSTBL.szFileId6,mmsPacketSend.getFileId6Value(),sizeof(senderDbMMSTBL.szFileId6)-1);

	if (nTelcoFlag == 1) {
		strncpy(senderDbMMSTBL.szButton1,mmsPacketSend.getButton1Value(),sizeof(senderDbMMSTBL.szButton1)-1);
	}
	/**
	strncpy(senderDbMMSTBL.szButton2,mmsPacketSend.getButton2Value(),sizeof(senderDbMMSTBL.szButton2)-1);
	strncpy(senderDbMMSTBL.szButton3,mmsPacketSend.getButton3Value(),sizeof(senderDbMMSTBL.szButton3)-1);
	strncpy(senderDbMMSTBL.szButton4,mmsPacketSend.getButton4Value(),sizeof(senderDbMMSTBL.szButton4)-1);
	strncpy(senderDbMMSTBL.szButton5,mmsPacketSend.getButton5Value(),sizeof(senderDbMMSTBL.szButton5)-1);
	strncpy(senderDbMMSTBL.szButton6,mmsPacketSend.getButton6Value(),sizeof(senderDbMMSTBL.szButton6)-1);
	strncpy(senderDbMMSTBL.szButton7,mmsPacketSend.getButton7Value(),sizeof(senderDbMMSTBL.szButton7)-1);
	strncpy(senderDbMMSTBL.szButton8,mmsPacketSend.getButton8Value(),sizeof(senderDbMMSTBL.szButton8)-1);
	strncpy(senderDbMMSTBL.szButton9,mmsPacketSend.getButton9Value(),sizeof(senderDbMMSTBL.szButton9)-1);
	strncpy(senderDbMMSTBL.szButton10,mmsPacketSend.getButton10Value(),sizeof(senderDbMMSTBL.szButton10)-1);
	strncpy(senderDbMMSTBL.szButton11,mmsPacketSend.getButton11Value(),sizeof(senderDbMMSTBL.szButton11)-1);
	strncpy(senderDbMMSTBL.szButton12,mmsPacketSend.getButton12Value(),sizeof(senderDbMMSTBL.szButton12)-1);
	**/
	
	strcpy(senderDbMMSTBL.szPtnSn,mmsPacketSend.getKeyValue());
	strcpy(senderDbMMSTBL.szResvData,mmsPacketSend.getExtendValue());
	
	strcpy(senderDbMMSTBL.szCid,szSenderID);
	
	if (bcFlag == 1)
	{
		if(strlen(mmsPacketSend.getHeadValue()) > 0)
		{
			strcpy(senderDbMMSTBL.szHead,mmsPacketSend.getHeadValue());
		}
		else
		{
			strcpy(senderDbMMSTBL.szHead,"0");	
		}
	}
	else
	{
		strcpy(senderDbMMSTBL.szHead,mmsPacketSend.getHeadValue());	
	}
	
	strcpy(senderDbMMSTBL.szFooter,mmsPacketSend.getFooterValue());
	strcpy(senderDbMMSTBL.szCopyAllowed,gSenderInfo.szCopyAllowed);
	strcpy(senderDbMMSTBL.szRcsConvert,gSenderInfo.szRcsConvert);
	strcpy(senderDbMMSTBL.szExpiryOption,gSenderInfo.szExpiryOption);
	strcpy(senderDbMMSTBL.szScheduleType,gSenderInfo.szScheduleType);
	strcpy(senderDbMMSTBL.szAgencyId,gSenderInfo.szAgencyId);
	//20230404
	//strcpy(senderDbMMSTBL.szAgencyKey,	gSenderInfo.szAgencyKey);
	//strcpy(senderDbMMSTBL.szBrandKey,	gSenderInfo.szBrandKey);
	
	senderDbMMSTBL.nMsgType 	= 0;
	senderDbMMSTBL.nPriority 	= priority;
	senderDbMMSTBL.nCtnId 		= ctnid;
	senderDbMMSTBL.nCtnType 	= mmsPacketSend.getCtnType();
	senderDbMMSTBL.nRgnRate 	= 65;
	senderDbMMSTBL.nInterval 	= 10;
	senderDbMMSTBL.nTextCnt 	= atoi(mmsPacketSend.getTextCntValue());
	senderDbMMSTBL.nImgCnt 		= atoi(mmsPacketSend.getImgCntValue());
	senderDbMMSTBL.nAugCnt 		= mmsPacketSend.getAugCnt();
	senderDbMMSTBL.nMpCnt 		= mmsPacketSend.getMpCnt();
	senderDbMMSTBL.nTelcoFlag 	= nTelcoFlag;
	senderDbMMSTBL.nMMSId 		= nMMSId;
	
	ret = g_oracle.setMMSTBL(senderDbMMSTBL);
	if (ret <= 0)
	{
		logPrintS(1,"[ERR] setMMSTBL mmsid[%lld] ret[%d]", nMMSId, ret);
	}
	
	return ret;
}


/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	if(mmsPacketSend.getImgCnt() == 0)
	{
		senderDbMMSID.ctnid = -1;
		return 0;
	}
		
	senderDbMMSID.ctnid = g_oracle.getCTNID();
	if(senderDbMMSID.ctnid <= 0 )
	{
		logPrintS(1,"[ERR] socket_domain get ctn id to db failed - ctnid[%d]", senderDbMMSID.ctnid);
		// 2013.12 LSY 보완 필요
		senderDbMMSID.ctnid = 999999999;
		
		return 0;
	}
	if(senderDbMMSID.ctnid <= 0)
	{
		return -1;
	}
	return 1;
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getRepCTNID2DB(CSenderDbMMSID& senderDbMMSID)
{
	int ret = 0;
	if(atoi(mmsPacketSend.getRepImgCntValue()) == 0)
	{
		senderDbMMSID.repctnid = -1;
		return 0;
	}
		
	senderDbMMSID.repctnid = g_oracle.getRepCTNID();
	if(senderDbMMSID.repctnid <= 0 )
	{
		logPrintS(1,"[ERR] socket_domain get rep ctn id to db failed - repctnid[%d]", senderDbMMSID.repctnid);
		// 2013.12 LSY 보완 필요
		senderDbMMSID.repctnid = 999999999;
		
		return 0;
	}
	if(senderDbMMSID.repctnid <= 0)
	{
		return -1;
	}
	return 1;
}
/**====================================================================================**/
// 2014020 make mmsid
/**====================================================================================**/
long long mkMMSID(void)
{
//	char	*pch;
//	pch		=(char*)malloc(sizeof(char)*100);
	char	pch[30];
	char	pchlast[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	int nRand;
	
//	time(&the_time);					//불필요한 초기 시간 제거용으로 사용
//	tm_ptr = localtime(&val.tv_sec);	//불필요한 초기 시간 제거용으로 사용	
//	gettimeofday(&val,NULL);			//불필요한 초기 시간 제거용으로 사용
//	
//	time(&the_time);
//	tm_ptr = localtime(&val.tv_sec);
//	gettimeofday(&val,NULL);

	memset(pch				,0x00		,sizeof(pch));	
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(pch, "%2d%02d%02d%02d%09ld"
				,tp.tm_mday+10
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);
		
//	sprintf(pch,"%2d%02d%02d%02d%06ld"
//				//,tm_ptr->tm_year+1900
//				//,tm_ptr->tm_mon+1
//				,tm_ptr->tm_mday+10
//				,tm_ptr->tm_hour
//				,tm_ptr->tm_min
//				,tm_ptr->tm_sec
//				,val.tv_usec
//				);
//	ulltm = atoll(pch);
	
	//srand(val.tv_usec);
	//srand(tmv.tv_nsec+S_PROCESS_NO);
	//nRand = (rand()%100);
	
	memset(pchlast		,0x00		,sizeof(pchlast));
	//sprintf(pchlast,"%.14s%02d",pch,nRand);
	sprintf(pchlast,"%.12s%04d", pch, proc_id);
	
	ulltm = atoll(pchlast);
//	free(pch);
	return ulltm;
	
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
int getMMSID2DB(CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	senderDbMMSID.mmsid = 0;
	memcpy(senderDbMMSID.szCid, cid, 10);
	senderDbMMSID.mmsid = g_oracle.getMMSID();

	if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
	{
		logPrintS(0,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		senderDbMMSID.mmsid = mkMMSID();
				
		//return 0;	// 20140212 : ADD return
		if(senderDbMMSID.mmsid == 0 || senderDbMMSID.mmsid < 0)
		return -1;
	}

	if( senderDbMMSID.mmsid < 0 )
	{
		return -1;
	}

	return 1;
	/**====================================================================================**/
}


int setRPTTBL2DB(long long nMMSId, int ctnid, int priority, int _code, char *code_text)
{
	int ret;
	time_t ThisT,LastT;
	CSenderDbMMSRPTTBL senderDbRPTTBL;
	time(&LastT);

	memset(&senderDbRPTTBL, 0x00, sizeof(CSenderDbMMSRPTTBL));

	senderDbRPTTBL.header.type = SETRPTTBL;
	senderDbRPTTBL.header.leng = sizeof(CSenderDbMMSRPTTBL) - sizeof(Header);
	
	senderDbRPTTBL.nMMSId = nMMSId;
	sprintf(senderDbRPTTBL.szCallBack, mmsPacketSend.getSenderValue());
	sprintf(senderDbRPTTBL.szDstAddr,mmsPacketSend.getReceiverValue());

	senderDbRPTTBL.res_code = _code;
	sprintf(senderDbRPTTBL.res_text, code_text);

	ret = g_oracle.setRPTTBL(senderDbRPTTBL);

	return ret;
}

int getTelcoId(char* szTelco, int nTelcoFlag)
{
	char* p;
	//int telcoArray[3];
	int telcoArray[5];
	int i=0;
	
	memset(telcoArray,0x00,sizeof(telcoArray));

	p = strtok(szTelco,"|");
	
	if( p == NULL )
	{
		return 1;
	}
	telcoArray[i++] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[i++]= atoi(p);
		//if( i >= 4 )
		if( i >= 5 )
		{
			break;
		}
	}

	//szTelco 다시 복구.버그 수정.2012.12.10.
	//sprintf(szTelco, "%d|%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3]);
	sprintf(szTelco, "%d|%d|%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3], telcoArray[4]);

	//SMS 문자를 우선으로 통신사를 체크한다. (LMS/MMS)
	if (nTelcoFlag  == 1)
	{
		return telcoArray[0];			//SMS
	}
	else if (nTelcoFlag  == 2)				//일반 LMS
	{
		return telcoArray[1];			//LMS
	}
	else if (nTelcoFlag  == 3)				//일반 MMS
	{
		return telcoArray[2];			//MMS
	}
	else if (nTelcoFlag  == 4)
	{
		return telcoArray[3];			//CONTENTS
	}
	else if (nTelcoFlag == 5)
	{
		return telcoArray[4];			//Image Template
	}

	return 1;
	
}


void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid,int bcFlag)
{
	char szMbaseIDL[40+1];
	
	memset(szMbaseIDL,0x00,sizeof(szMbaseIDL));
	
	/*if(bcFlag == 1)
	{
		strncpy(szMbaseIDL,mmsPacketSend.getSenderKeyValue(),sizeof(szMbaseIDL)-1);
	}
	else
	{
		strncpy(szMbaseIDL,mmsPacketSend.getMBaseIdValue(),sizeof(szMbaseIDL)-1);
	}*/
	
	strncpy(szMbaseIDL,mmsPacketSend.getMBaseIdValue(),sizeof(szMbaseIDL)-1);
	
	logPrintS(1,"[INF] send message mmsid[%lld]mbaseid[%s]key[%s]extend[%s]subject1[%s]dst[]call[%s]",
            mmsid,
            //mmsPacketSend.getMBaseIdValue(),
            szMbaseIDL,
            mmsPacketSend.getKeyValue(),
            mmsPacketSend.getExtendValue(),
            mmsPacketSend.getMsgTitle1Value(),
            /*mmsPacketSend.getSubjectValue(),*/
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue()
            /*mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            //mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getHeadValue(),
            mmsPacketSend.getFooterValue(),
            gSenderInfo.szCopyAllowed,
			gSenderInfo.szRcsConvert,
			gSenderInfo.szExpiryOption,
			gSenderInfo.szScheduleType,
			gSenderInfo.szAgencyId*/
            );
}

void writeLogCtnData(CMMSPacketSend& mmsPacketSend,int ctnid)
{
    logPrintS(1,"[INF] send ctn message cid[%d]key[%s]",
            ctnid,
            mmsPacketSend.getKeyValue()
            );
}

SenderProcess::SenderProcess()
{
	bDayWarnCheck=false;
	bMonWarnCheck=false;
}

void SenderProcess::SenderMain(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	CProcessInfo processInfo;
	CMonitor monitor;

	memset(&processInfo,0x00,sizeof(processInfo));
	memset(&gSenderInfo,0x00,sizeof(gSenderInfo));
	  
	strcpy(processInfo.processName,logonDbInfo.szSenderName);
	get_timestring("%04d%02d%02d%02d%02d%02d",time(NULL),processInfo.startTime);
	
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
	
	util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo);
	util.findValueParse(logonDbInfo.szReserve, "mms_yn",  gSenderInfo.szSmsFlag);

	//변작금지 flag 추가
	util.findValueParse(logonDbInfo.szReserve, "chk_callback", gSenderInfo.szChkCallback);
	
	//rcs 추가 기준 정보
	util.findValueParse(logonDbInfo.szReserve, "agency_id", gSenderInfo.szAgencyId);
	util.findValueParse(logonDbInfo.szReserve, "expiry_option", gSenderInfo.szExpiryOption);
	util.findValueParse(logonDbInfo.szReserve, "copy_allowed", gSenderInfo.szCopyAllowed);
	util.findValueParse(logonDbInfo.szReserve, "schedule_type", gSenderInfo.szScheduleType);
	util.findValueParse(logonDbInfo.szReserve, "convert", gSenderInfo.szRcsConvert);
	//20230322 add
	//util.findValueParse(logonDbInfo.szReserve, "brand_key", gSenderInfo.szBrandKey);
	util.findValueParse(logonDbInfo.szReserve, "agency_key", gSenderInfo.szAgencyKey);
	//강제실패
	util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
	//강제실패
	util.findValueParse(logonDbInfo.szReserve, "brand_id",  gSenderInfo.szBrandId);
	
	strcpy(gSenderInfo.szUrlTelcoInfo,"0");
	strcpy(gSenderInfo.szUrlFlag,"0");
	
	logPrintS(0,"[INF] send process sender main start sockfd[%d]CID[%s]processInfo.startTime[%s]pid[%s]logonDbInfo.Reserve[%s]senderInfo[%s]smsFlag[%s]UrlTelcoInfo[%s]UrlFlag[%s]ChkCallback[%s]AgencyId[%s]ExpiryOption[%s]szCopyAllowed[%s]szScheduleType[%s]szRcsConvert[%s]szBlockYN[%s]szBrandId[%s]szAgencyKey[%s]"
            ,sockfd
            ,logonDbInfo.szCID
            ,processInfo.startTime
            ,processInfo.szPid
            ,logonDbInfo.szReserve
            ,gSenderInfo.szSmsTelcoInfo
            ,gSenderInfo.szSmsFlag
            ,gSenderInfo.szUrlTelcoInfo
            ,gSenderInfo.szUrlFlag
			,gSenderInfo.szChkCallback
			,gSenderInfo.szAgencyId
			,gSenderInfo.szExpiryOption
			,gSenderInfo.szCopyAllowed
			,gSenderInfo.szScheduleType
			,gSenderInfo.szRcsConvert
			,gSenderInfo.szBlockYN
			,gSenderInfo.szBrandId
			//,gSenderInfo.szBrandKey
			,gSenderInfo.szAgencyKey
			);
	
	util.displayLogonDbInfo(logonDbInfo,_MONILOG);

	//20150610 변작금지-콜백리스트 가져오기
	if(strncmp(gSenderInfo.szChkCallback, "1", 1) == 0)
	{
		logPrintS(1, "ChkCallback[%d]:회신번호 검사", gSenderInfo.szChkCallback);
		ret = g_oracle.selectTblCallback(checkCallback.set_callback_list, logonDbInfo.nmPID);
		if(ret < 0)
		{
			logPrintS(1, "ret[%d]g_oracle.selectTblCallback() error", ret);
			return;
		}
	}
	else if(strncmp(gSenderInfo.szChkCallback, "2", 1) == 0)
	{
		logPrintS(1, "ChkCallback[%d]:번호체계 검사", gSenderInfo.szChkCallback);
		ret = loadDialCodeAll();

		if(ret < 0)
		{
			logPrintS(1, "ret[%d]load dial code error", ret);
			return;
		}
	}
	

	CKSSocket hRemoteSock;

	int recvLen;

	std::map<string, string>::iterator itb;
   
	hRemoteSock.attach(sockfd);
   
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] socket_domain create failed - CID[%s]classify[%c]domain_path[%s]", logonDbInfo.szCID, logonDbInfo.classify, gConf.domainPath);
        goto SenderEND;
	}
	
	monitor.Init("logon7", "sender", processInfo.processName, logonDbInfo.szCID, logonDbInfo.nmPID, logonDbInfo.szIP);
	time(&SLastTLink);
	
	nCurAccCnt = 0;

	memset(szLimitTime, 0x00, sizeof(szLimitTime));
	get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitTime);	// 현재 날짜 구하기
	memset(szLimitCurTime, 0x00, sizeof(szLimitCurTime));
	
	//ret = g_oracle.selectBrandKeyList(mapBrandKey);
#if (0)
	//std::map<string, string>::iterator itb;
	logPrintS(1, "[DEB] mapBrandKey size [%d]", mapBrandKey.size());
	for (itb=mapBrandKey.begin(); itb != mapBrandKey.end(); ++itb)
	{
		logPrintS(1,"[DEB] mapBrandKey : key [%s] value [%s]", itb->first.c_str(), itb->second.c_str() );
	}
	
	if( ret < 0 )
	{
		logPrintS(0,"[ERR] selectBrandKeyList failed [%d]", ret );
        goto SenderEND;
	}
#endif


    
	while(bSActive)
	{
		//logPrintS(1,"[STP] -0- []");
				
		// 시간측정로그 : 메시지 수신전 시간
		/*				
	 	struct tm *d;
		struct timeval val;
		gettimeofday(&val, NULL);
		d=localtime(&val.tv_sec);		
		logPrintS(1,"0[Msg Recv Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		*/					
		ret = hRemoteSock.recvAllMsg(14);
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintS(1,"[ERR] socket read msg failed - [%s][%d]",hRemoteSock.getErrMsg(), errno);
			logPrintS(0,"[ERR] socket read msg failed - [%s][%d]",hRemoteSock.getErrMsg(), errno);
		
			//운영팀 알람 요청 20160114
			char szTemp[256] = {0x00,};
			sprintf(szTemp,"[%s]Client connect Error!!-CID[%s]", logonDbInfo.szIP, logonDbInfo.szCID);
			logPrintS(1,"[%s]", szTemp);

			/*if(Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
			{
				monitoring("[ERR] alert2admin daily service limit send failed",0,0);
			}*/

			goto SenderEND;
		}
		      
		if( ret == 0 )
		{
			//wait_a_moment(logonDbInfo.nmCNT);
			ret = admin.checkPacket(processInfo,logonDbInfo,sum);// check admin packet
			if( ret < 0 )
			{
				logPrintS(0,"[ERR] socket_domain packet check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				goto SenderEND;
			}
			//logPrintS(1,"[STP] -1- []");
	
			switch(ret) 
			{
				case 3: // end
					bSActive = false;
					continue;

				case 5: // info
					memset(gSenderInfo.szSmsTelcoInfo, 0x00, sizeof(gSenderInfo.szSmsTelcoInfo)); 
					memset(gSenderInfo.szSmsFlag, 0x00, sizeof(gSenderInfo.szSmsFlag)); 
					memset(gSenderInfo.szAgencyId, 0x00, sizeof(gSenderInfo.szAgencyId)); 
					memset(gSenderInfo.szExpiryOption, 0x00, sizeof(gSenderInfo.szExpiryOption)); 
					memset(gSenderInfo.szCopyAllowed, 0x00, sizeof(gSenderInfo.szCopyAllowed)); 
					memset(gSenderInfo.szScheduleType, 0x00, sizeof(gSenderInfo.szScheduleType)); 
					memset(gSenderInfo.szRcsConvert, 0x00, sizeof(gSenderInfo.szRcsConvert)); 
					memset(gSenderInfo.szBlockYN, 0x00, sizeof(gSenderInfo.szBlockYN));
					memset(gSenderInfo.szBrandId, 0x00, sizeof(gSenderInfo.szBrandId));
					//memset(gSenderInfo.szBrandKey, 0x00, sizeof(gSenderInfo.szBrandKey));
					memset(gSenderInfo.szAgencyKey, 0x00, sizeof(gSenderInfo.szAgencyKey));
					util.findValueParse(logonDbInfo.szReserve, "mms_tel" ,gSenderInfo.szSmsTelcoInfo); 
					util.findValueParse(logonDbInfo.szReserve, "mms_yn"	,gSenderInfo.szSmsFlag);
					
					//rcs 추가 기준 정보
					util.findValueParse(logonDbInfo.szReserve, "agency_id", gSenderInfo.szAgencyId);
					util.findValueParse(logonDbInfo.szReserve, "expiry_option", gSenderInfo.szExpiryOption);
					util.findValueParse(logonDbInfo.szReserve, "copy_allowed", gSenderInfo.szCopyAllowed);
					util.findValueParse(logonDbInfo.szReserve, "schedule_type", gSenderInfo.szScheduleType);
					util.findValueParse(logonDbInfo.szReserve, "convert", gSenderInfo.szRcsConvert);
					//20230322 add
					//util.findValueParse(logonDbInfo.szReserve, "brand_key", gSenderInfo.szBrandKey);
					util.findValueParse(logonDbInfo.szReserve, "agency_key", gSenderInfo.szAgencyKey);
					//강제실패
					util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
					//brand id
					util.findValueParse(logonDbInfo.szReserve, "brand_id",  gSenderInfo.szBrandId);
					//20170706 admin 시 한도제한 초기화오류로 추가
                	nCurAccCnt = 0;
					logPrintS(0,"[INF] info modify ok");
					break;
				case 6: //callback list reload
					checkCallback.loadCallbackList(processInfo, logonDbInfo);
					logPrintS(0,"[INF] callback info reload ok[%s:%d]", __FILE__, __LINE__);
					break;

				case 7: //chk_callback state reload
					{
						memset(gSenderInfo.szChkCallback, 0x00, sizeof(gSenderInfo.szChkCallback));
						util.findValueParse(logonDbInfo.szReserve, "chk_callback", gSenderInfo.szChkCallback);
						int loadRet = 0;
						logPrintS(0, "[INF] chk_callback[%s] modified ok[%s:%d]", gSenderInfo.szChkCallback, __FILE__, __LINE__);

						if(strcmp(gSenderInfo.szChkCallback, "1") == 0)
						{
							checkCallback.set_callback_list.clear();

							loadRet = g_oracle.selectTblCallback(checkCallback.set_callback_list, logonDbInfo.nmPID);
							if(loadRet < 0)
							{
								logPrintS(0, "loadCallbackList error[%s:%d]", __FILE__, __LINE__);
								return;
							}
						}   
						else if(strcmp(gSenderInfo.szChkCallback, "2") == 0)
						{
							loadRet = loadDialCodeAll();
							if(loadRet < 0)
							{
								logPrintS(0, "loadDialCodeAll error[%s:%d]", __FILE__, __LINE__);
								return;
							}
						}
					}  

					break;
				default:
					break;
			}
	
			time(&SThisT);	
			ret = (int)difftime(SThisT,monLastT);
			if( ret > 30 )
			{
				monitor.setDataSum(sum);
				monitor.setCurDate();
				monitor.send(gConf.monitorName);
				time(&monLastT);
				sum=0;
			}
			continue; // no data
		}
		 
		//logPrintS(1,"[STP] -3- []");
		
		// write log
		//로그 주석 처리.2011.11.23.
#ifdef _DEBUG
	    _monPrint(_MONILOG,(char*)hRemoteSock.getMsg());
	    printf("\n\n%s\n\n", (char*)hRemoteSock.getMsg());
#endif
			
#ifdef TIME
		/* gettimeofday */
		struct timeval timefirst, timesecond;
		struct timezone tzp;
		int secBuf, microsecBuf;
		float timeBuf;
		
		gettimeofday(&timefirst,&tzp);
		/* gettimeofday */
#endif
		//logPrintS(1,"nCurAccCnt(%d)",nCurAccCnt);
		
		ret = classifyS(monitor, processInfo, logonDbInfo, db, hRemoteSock);
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] classifyS Error ret [%d]",ret);
			goto SenderEND;
		}
		//logPrintS(1,"[STP] -4- []");
			
#ifdef TIME
		gettimeofday(&timesecond,&tzp);
		secBuf 		= (timesecond.tv_sec - timefirst.tv_sec);
		microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
		timeBuf 	= microsecBuf;
		timeBuf 	= timeBuf / 1000000;
		timeBuf 	= timeBuf + secBuf;
		logPrintS(0,"senderProcess db time [%f]",timeBuf);
#endif
    }

SenderEND:
	logPrintS(0,"[INF] socket END sockfd[%d]CID[%s]", hRemoteSock.getSockfd(), logonDbInfo.szCID);
	hRemoteSock.close();
	
	return;
}


int SenderProcess::classifyS(CMonitor& monitor,CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,CKSSocket& db,CKSSocket& hRemoteSock) 
{
	int ret = 0;
	int rptRet = 0;
	char szYYYYMM[32];
	string strPacketHeader;
	int bcFlag = 0;
	
	CSenderDbMMSID senderDbMMSID;
	CBcastData bCastData;      	

	strPacketHeader = "";
	strPacketHeader.reserve(0);
			
	// 시간측정로그 : classifyS strPacketHeader.insert 시작
	struct tm *d;
	struct timeval val;
	// gettimeofday(&val, NULL);
	// d=localtime(&val.tv_sec);		
	//logPrintS(1,"4[classifyS strPacketHeader.insert Start]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
  	
  	memset(&senderDbMMSID,0x00,sizeof(senderDbMMSID));  	
  	
	strPacketHeader.insert(0,hRemoteSock.getMsg(),30);
	
#if (DEBUG >= 5)
		logPrintS(1,"[DBG] strPacketHeader [%s]", strPacketHeader.c_str());
#endif	
  
	if( strstr(strPacketHeader.c_str(),"BEGIN PING\r\n") ) 
	{
		fflush(stdout);
		//logPrintS(0,"[INF] socket link recv");
		
		ret = sendPong(hRemoteSock);
		monitor.setLinkTime();
		if( ret < 0 ) 
		{
			return ret;
		}
	} 
	else if ( strstr(strPacketHeader.c_str(),"BEGIN RCSSEND\r\n")||strstr(strPacketHeader.c_str(),"BEGIN MMSSEND\r\n"))  
	{          
		if(strstr(strPacketHeader.c_str(),"BEGIN RCSSEND\r\n"))
		{
			bcFlag=2;
		}
		else if(strstr(strPacketHeader.c_str(),"BEGIN MMSSEND\r\n"))
		{
			bcFlag=1;	
		}                            
		//cout<<"packet ["<<hRemoteSock.getMsg()<<"]"<<endl;
		/* 
		 * 20141210 운영팀 요청사항으로 
		 * 처리오류시 sendAck 전송시에는 종료 안함
		 */
		// 20140212 : mmsPacketSend.parse fix return 100
		/*
			return parse result
				-1 : MData Header info error
				 0 : OK
		*/
		//logPrintS(1,"[DBG] -0- []");

#if (DEBUG >= 5)
		logPrintS(1,"[DBG] -0- RepMsgBody [%s]", mmsPacketSend.getRepMsgBodyValue());
#endif

#if (DEBUG >= 5)
		logPrintS(1,"[DBG] parse [%.1900s]", hRemoteSock.getMsg());
#endif
		
		ret = mmsPacketSend.parse((char*)hRemoteSock.getMsg());		
		
		//ret = -1;		//test code
		
		if( ret != 100 ) 
		{
			logPrintS(0,"[ERR] packet parse failed - ErrMsg[%s]",mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"Msg Parsing Error");
			//return ret;
			return 0;
		}
		//logPrintS(1,"[DBG] -1- []");
		
		//getMMSID2DB(db,senderDbMMSID,szSenderID);//디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/
		ret = getMMSID2DB(senderDbMMSID,szSenderID);
		
		//ret = -1;		//test code
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get RCSID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"DB GET RCS ID FAILED.");
			
			rptRet = setMMSRPTTBL(0,0,mmsPacketSend,3001,"GetRCSIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getMMSID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			}
			//return ret;
			return 0;
		}           
		
		
		//getCTNID2DB(db,senderDbMMSID,szSenderID);// 디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/

		ret = getRepCTNID2DB(senderDbMMSID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get REP CTNID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"DB GET REP CTN ID FAILED.");
			
			rptRet = setMMSRPTTBL(0,0,mmsPacketSend,3016,"GetRepCtnIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getRepCTNID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			}
			//return ret;
			return 0;
		}	
		/**====================================================================================**/
		writeLogMMSData(mmsPacketSend,senderDbMMSID.mmsid,senderDbMMSID.repctnid,bcFlag);// LOG write

		CMMSFileProcess mmsFileProcess;// write file object
 	
		memset(szYYYYMM	,0x00	,sizeof(szYYYYMM));  //CCL(szYYYYMM);
		get_timestring("%04d%02d",time(NULL),szYYYYMM);
		trim(szYYYYMM,strlen(szYYYYMM));

		//mmsFileProcess.write(db
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/


	// 시간측정로그 : 메시지 쓰기전
						
	 	//struct tm *d;
		//struct timeval val;
		//gettimeofday(&val, NULL);
		//d=localtime(&val.tv_sec);		
		//logPrintS(1,"0[Msg writeTxt Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",
		//		d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
			
		ret = mmsFileProcess.writeTxt(db
              					,mmsPacketSend
              					,gConf.ContentPath
              					,senderDbMMSID.mmsid
              					,szYYYYMM
              					,szSenderID
              					,senderDbMMSID.repctnid
              					,gConf.dbRequestTimeOut
              					,senderDbDomainName
              					);
		
		//d=localtime(&val.tv_sec);		
		//logPrintS(1,"0[Msg writeTxt After]%04d%02d%02d,%02d:%02d:%02d-%06ld",
		//		d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
				
		/*if(strcmp(mmsPacketSend.getMBaseIdValue(), "SMwThT00") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "SMwThM00") == 0||
		   strcmp(mmsPacketSend.getMBaseIdValue(), "SMwLhX00") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "SMwRhX00") == 0||
		   strcmp(mmsPacketSend.getMBaseIdValue(), "CMwMhM0300") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "CMwMhM0400") == 0||
		   strcmp(mmsPacketSend.getMBaseIdValue(), "CMwMhM0500") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "CMwMhM0600") == 0||
		   strcmp(mmsPacketSend.getMBaseIdValue(), "CMwShS0300") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "CMwShS0400") == 0||
		   strcmp(mmsPacketSend.getMBaseIdValue(), "CMwShS0500") == 0||strcmp(mmsPacketSend.getMBaseIdValue(), "CMwShS0600") == 0)		
		{
			if(atoi(mmsPacketSend.getTextCntValue())==0 && atoi(mmsPacketSend.getImgCntValue())==0)
			{
				sendAck(hRemoteSock,mmsPacketSend,30002,senderDbMMSID.repctnid ,"메세지 내용없음");	
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,30002,"NoText.",szSenderID);
			
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] contents size setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				
				return 0;
			}
		}*/		
		//		ret = -1;		//test code
		if(ret < 0 )
		{
			logPrintS(0,"[ERR] file write failed [%d]", ret);
			/*
			 * ret == -2 : IMG 사이즈 1M 초과 오류
			 */
			if ( ret == -2)
			{
				sendAck(hRemoteSock,mmsPacketSend,3003,senderDbMMSID.repctnid ,"컨텐츠 크기 초과");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3003,"OverContentsSize.",szSenderID);
			
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] contents size setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
			}
			else
			{
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"FILE WRITE FAILED.");
				
				rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3004,"ContentsFileErr.",szSenderID);
			
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] contents write setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
			//return ret;
			}
			return 0;
		}
	
		/**====================================================================================**/
		//logPrintS(1,"[DBG] -2- [%lld]",senderDbMMSID.mmsid);
		//setMMSTBL2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		if(strncmp(gSenderInfo.szBlockYN,"Y",1)==0)
    	{
    		ret =  sendAck(hRemoteSock,mmsPacketSend,3015, senderDbMMSID.repctnid ,"Fail");
		
			if ( ret  < 0 )
			{
				logPrintS(0,"[ERR] block socket send ack failed");
			}

			rptRet = setMMSRPTTBL( 0, senderDbMMSID.mmsid, mmsPacketSend, 3015, "SendBlocked.", szSenderID);
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSRPTTBL block cid[%s] ptn_sn[%s] mmsid[%lld]", 
					szSenderID, 
					mmsPacketSend.getKeyValue(), 
					senderDbMMSID.mmsid
					);
			}
			return 0;
    	}
    	else
    	{
    		ret = setMMSTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.repctnid, mmsFileProcess, logonDbInfo.nmPRT,bcFlag);  // send table
			
			//ret = -1;		//test code
			
			if ( ret < 0 )
			{
				if (strlen(mmsPacketSend.getSenderValue()) == 0)
				{
					logPrintS(0,"[ERR] 발신번호 오류[%s]", mmsPacketSend.getSenderValue());
					sendAck(hRemoteSock,mmsPacketSend,3005,senderDbMMSID.repctnid ,"발신번호 오류");
					
					rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3005,"InsertRCSTBLFailedByCallback.",szSenderID);
					
					if( rptRet < 0 )
					{
						logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed by Callack cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
					}
				}
				else if(ret == -3)
				{
					logPrintS(0,"[ERR] setMMSTBL2DB replace message over byte error mmsid[%lld][%s]",senderDbMMSID.mmsid,mmsPacketSend.getMBaseIdValue());
					sendAck(hRemoteSock,mmsPacketSend,3018,senderDbMMSID.repctnid ,"RCS 전환전송 메세지 바이트 오류");
					
					rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3018,"InsertRCSTBLFailedByRepMsgByteOver.",szSenderID);
					
					if( rptRet < 0 )
					{
						logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
					}
				}
				else if(ret == -2)
				{
					logPrintS(0,"[ERR] MMS replace img count 0 error mmsid[%lld][%s]",senderDbMMSID.mmsid,mmsPacketSend.getMBaseIdValue());
					sendAck(hRemoteSock,mmsPacketSend,3017,senderDbMMSID.repctnid ,"RCS 전환전송 대체이미지 0개 오류");
					
					rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3017,"InsertRCSTBLFailedByRepImgCnt.",szSenderID);
					
					if( rptRet < 0 )
					{
						logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
					}
				}
				else
				{
					logPrintS(0,"[ERR] db insert table MMSTBL failed mmsid[%lld][%s]",senderDbMMSID.mmsid,mmsPacketSend.getMBaseIdValue());
					sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"DB INSERT RCSTBL FAILED.");
					
					rptRet = setMMSRPTTBL(0,senderDbMMSID.mmsid,mmsPacketSend,3006,"InsertRCSTBLFailed.",szSenderID);
					
					if( rptRet < 0 )
					{
						logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
					}
				}
				//return ret;
				return 0;
			}
		}
		
		//회신 번호 검사
		if(strncmp(gSenderInfo.szChkCallback, "1", 1) == 0) 
		{
			/*char szExeCallBack[16+1];
			
			memset(szExeCallBack,0x00,sizeof(szExeCallBack));
			
			if(bcFlag == 1)
			{
				strcpy(szExeCallBack,szBcCallBack);
			}
			else
			{
				strcpy(szExeCallBack,mmsPacketSend.getSenderValue());	
			}*/
			
			//if(checkCallback.findCallback(szExeCallBack) < 0)
			if(checkCallback.findCallback(mmsPacketSend.getSenderValue()) < 0)
			{
				//logPrintS(0, "[ERR] [%s]회신 번호 리스트에 존재하지 않습니다.", szExeCallBack);
				logPrintS(0, "[ERR] [%s]회신 번호 리스트에 존재하지 않습니다.", mmsPacketSend.getSenderValue());
				//ret = setRPTTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.repctnid, logonDbInfo.nmPRT, 3007, "NO CALLBACK INFO");
				sendAck(hRemoteSock, mmsPacketSend, 3007, senderDbMMSID.repctnid, "NO CALLBACK INFO");
				
				rptRet = setMMSRPTTBL(1,senderDbMMSID.mmsid,mmsPacketSend,3007,"NoCallbackInfo.",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] checkCallback 1 setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				//return ret;
				return 0;
			}
		}
		else if(strncmp(gSenderInfo.szChkCallback, "2", 1) == 0)
		{
			/*char szExeCallBack[16+1];
			
			memset(szExeCallBack,0x00,sizeof(szExeCallBack));
			
			if(bcFlag == 1)
			{
				strcpy(szExeCallBack,szBcCallBack);
			}
			else
			{
				strcpy(szExeCallBack,mmsPacketSend.getSenderValue());	
			}*/
			
			//if(checkCallback.examineCallback(szExeCallBack) < 0)
			if(checkCallback.examineCallback(mmsPacketSend.getSenderValue()) < 0)
			{
				logPrintS(0, "[ERR] [%s]", checkCallback.get_errorMsg().c_str());
				//ret = setRPTTBL2DB(senderDbMMSID.mmsid, senderDbMMSID.repctnid,logonDbInfo.nmPRT, 3008, "CALLBACK IS NOT VALID");
				sendAck(hRemoteSock, mmsPacketSend, 3008, senderDbMMSID.repctnid, "CALLBACK IS NOT VALID");
				
				rptRet = setMMSRPTTBL(1,senderDbMMSID.mmsid,mmsPacketSend,3008,"CallbackIsNotValid",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] checkCallback 2 setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				//return ret;
				return 0;
			}
		}
		
		//logPrintS(1,"[DBG] -3- [%lld]",senderDbMMSID.mmsid);
		/**====================================================================================**/
		/*
			send queue
			1. 원본.2012.10.09 setMMSMSG2DB(db,senderDbMMSID.mmsid, senderDbMMSID.repctnid, mmsFileProcess,logonDbInfo.nmPRT);
			2. CID 값 추가.2012.10.09
		*/
		//setMMSMSG2DB(db, senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
		/**====================================================================================**/
		//20140212 ADD if 
		/**====================================================================================**/
		ret = setMMSMSG2DB(senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.repctnid, mmsFileProcess,logonDbInfo.nmPRT,bcFlag);
		
		//ret = -1;		//test code
		
		if ( ret < 0 )
		{
			logPrintS(0,"[ERR] db insert MMS MSG failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.repctnid ,"DB INSERT RCS MSG FAILED.");
			
			rptRet = setMMSRPTTBL(1,senderDbMMSID.mmsid,mmsPacketSend,3009,"InsertRCSMSGFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSMSG2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			}
			
			//return ret;
			return 0;
		}
		//logPrintS(1,"[DBG] -4- [%lld]",senderDbMMSID.mmsid);
		
		/**====================================================================================**/
		/*
		 *	SendAck 부분을 밑에서 위로 옮김.2012.08.20
		 *	동보 전송일 경우 레포트가 먼저 수신되는 경우가 생겨 옮김
		 */
		 /**====================================================================================**/
		ret =  sendAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.repctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket send ack failed");
			return ret;
		}
		logPrintS(1,"[DBG] -5- [%lld] : Send Ack End",senderDbMMSID.mmsid);
		
		monitor.setDataTime();    
		nCurAccCnt++;
        
		ret =	SenderLimit(logonDbInfo);
		
		if(ret == -1)
		{
			logPrintS(0,"[ERR] limit SendreLimit ret [%d]",ret);
			return -1;
		}		//	logPrintS(1,"ret(%d)nCurAccCnt(%d)",ret,nCurAccCnt);
		//logPrintS(1,"[DBG] -6- [%lld]",senderDbMMSID.mmsid);

	}
	else if (strstr(strPacketHeader.c_str(), "BEGIN RCSIMGURL\r\n")) 
	{
		/* 1. parsing telegram */
		ret = mmsPacketSend.parse((char *) hRemoteSock.getMsg());
		
		//ret = -1;		//test code
		
		if (ret != 100) {
			logPrintS(0, "[ERR] RCSIMGURL packet parse failed - ErrMsg[%s]",
					mmsPacketSend.getErrorMsg());
			sendCtnAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid,
					"ctn message parsing error");
			return 0;
			//return ret;
		}

		ret = getCTNID2DB(senderDbMMSID,szSenderID);
		
		//ret = -1;		//test code
		
		if(ret < 0)
		{
			logPrintS(0, "[ERR] db select get CTNID failed cid[%s] ptn_sn[%s] ",
			          szSenderID, mmsPacketSend.getKeyValue());

			sendCtnAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid, "DB GET CTN ID FAILED.");
			
			rptRet = setMMSCTNRPTTBL(0, senderDbMMSID.ctnid, mmsPacketSend, 3010, "GetCTNIDFailed.", szSenderID);
			if(rptRet < 0)
			{
				logPrintS(0, "[ERR] getCTNID2DB setMMSCTNRPTTBL failed cid[%s] ptn_sn[%s] ctnid[%d]",
				          szSenderID, mmsPacketSend.getKeyValue(), senderDbMMSID.ctnid);
			}
			//return ret;
			return 0;
		}	
		
		writeLogCtnData(mmsPacketSend,senderDbMMSID.ctnid);// LOG write

		/* 2. save image file */
		memset(szYYYYMM, 0x00, sizeof(szYYYYMM)); //CCL(szYYYYMM);
		get_timestring("%04d%02d", time(NULL), szYYYYMM);
		trim(szYYYYMM, strlen(szYYYYMM));

		CMMSFileProcess mmsFileProcess; // write file object
		ret = mmsFileProcess.writeImg(db, mmsPacketSend, gConf.ContentPath,
				szYYYYMM, szSenderID, senderDbMMSID.ctnid,
				gConf.dbRequestTimeOut, senderDbDomainName);
				
		//ret = -1;		//test code		
				
		if (ret < 0) {
			logPrintS(0, "[ERR] file write failed [%d]", ret);

			if (ret == -2) {
				/* Image size is over 1 Mb */
				sendCtnAck(hRemoteSock, mmsPacketSend, 3011, senderDbMMSID.ctnid,
						"image size is over 1M");

				rptRet = setMMSCTNRPTTBL(0, senderDbMMSID.ctnid, mmsPacketSend,
						3011, "OverContentsSize.", szSenderID);
				if (rptRet < 0) {
					logPrintS(0, "[ERR] contents size setMMSCTNRPTTBL failed "
							"cid[%s] ptn_sn[%s] ctnid[%d]", szSenderID,
							mmsPacketSend.getKeyValue(), senderDbMMSID.ctnid);
				}
			} else {
				/* Image file is damaged */
				sendCtnAck(hRemoteSock, mmsPacketSend, ret, senderDbMMSID.ctnid,
						"CTN FILE WRITE FAILED.");

				rptRet = setMMSCTNRPTTBL(0, senderDbMMSID.ctnid, mmsPacketSend,
						3012, "ContentsFileErr.", szSenderID);
				if (rptRet < 0) {
					logPrintS(0, "[ERR] contents write setMMSCTNRPTTBL failed"
							" cid[%s] ptn_sn[%s] ctnid[%d]", szSenderID,
							mmsPacketSend.getKeyValue(), senderDbMMSID.ctnid);
				}
			}
			//return ret;
			return 0;
		}
		
		/**====================================================================================**/
		//logPrintS(1,"[DBG] -1- [%lld] : File write end",senderDbMMSID.mmsid);
	

		//setMMSCTNTBL2DB(db,mmsFileProcess);// insert CTL
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		if(strncmp(gSenderInfo.szBlockYN,"Y",1)==0)
    	{
    		ret =  sendCtnAck(hRemoteSock,mmsPacketSend,3019,senderDbMMSID.ctnid ,"CtnFail");
		
			if ( ret  < 0 )
			{
				logPrintS(0,"[ERR] block socket send ctn ack failed");
			}

			rptRet = setMMSCTNRPTTBL(0, senderDbMMSID.ctnid, mmsPacketSend,3019,"SendCtnBlocked.",szSenderID);
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSCTNRPTTBL block cid[%s] ptn_sn[%s] ctnid[%d]", 
					szSenderID, 
					mmsPacketSend.getKeyValue(), 
					senderDbMMSID.ctnid
					);
			}
			return 0;
    	}
    	else
    	{
			ret = setMMSCTNTBL2DB(mmsFileProcess,mmsPacketSend);
			
			//ret = -1;		//test code		
			
   			if ( ret < 0)
			{
				logPrintS(0,"[ERR] db insert table content failed");
				sendCtnAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT CONTENT TABLE FAILED.");
				
				rptRet = setMMSCTNRPTTBL(0, senderDbMMSID.ctnid, mmsPacketSend,3013,"InsertCNTTBLFailed.",szSenderID);
					
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] setMMSCTNTBL2DB setMMSCTNRPTTBL failed cid[%s] ptn_sn[%s] ctnid[%d]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.ctnid);
				}
				//return ret;
				return 0;
			}	
		}
		//setMMSCTNTBL2DB(db,mmsFileProcess);// insert CTL
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		ret = setMMSCTNMSG2DB(senderDbMMSID.szCid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
		
		//ret = -1;		//test code	
		
		if ( ret < 0)
		{
			logPrintS(0,"[ERR] db insert queue content failed");
			sendCtnAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT CONTENT MSG FAILED.");
			
			rptRet = setMMSCTNRPTTBL(1, senderDbMMSID.ctnid, mmsPacketSend,3014,"InsertCTNRCSMSGFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSCTNMSG2DB setMMSCTNRPTTBL failed cid[%s] ptn_sn[%s] ctnid[%d]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.ctnid);
			}
			
			//return ret;
			return 0;
		}
		
		/**====================================================================================**/
		/*
		 *	SendAck 부분을 밑에서 위로 옮김.2012.08.20
		 *	동보 전송일 경우 레포트가 먼저 수신되는 경우가 생겨 옮김
		 */
		/**====================================================================================**/
		ret =  sendCtnAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.ctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket ctn send ack failed");
		}
		logPrintS(1,"[DBG] ctnid[%d] : Ctn Send Ack End",senderDbMMSID.ctnid);
		
	}	 
	else  // error
	{
		logPrintS(0,"[INF] invalid msg header data - [%s] ", strPacketHeader.c_str());

		fflush(stdout);
		ret = -1;
	}

	return ret;
}								


int SenderProcess::SenderLimit(CLogonDbInfo& logonDbInfo)
{
	int ret;
	char szTemp[256];
	
	//* < brief 발송 제한 체크
	if (atoi(logonDbInfo.szLimitType) != 0)
	{
		memset(szTemp	,0x00	,sizeof(szTemp));
	
		get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitCurTime);	// 현재 날짜 구하기
		
		if (strcmp(szLimitTime,"") == 0)
		{
			strcpy(szLimitTime,szLimitCurTime);	// 년월일 값 구하기
		}

		ret = LimitCheck(logonDbInfo);
		//카운트 계산에 따른 서비스 제한 및 알림 기능 수행
		// logPrintS(1,"ret(%d)",ret);
		switch (ret)
		{
			case 9 : // 일 변경에 따른 누적 카운트 초기화 및 프로시저 실행
			case 10 : // 월 변경에 따른 누적 카운트 초기화 및 프로시저 실행
				if (ret == 9)
					logPrintS(1,"[INF]day change total count reset and run");
				if (ret == 10)
					logPrintS(1,"[INF]month change total count reset and run");
	
				//발송 제한 변수 초기화
				bDayWarnCheck = false;
				bMonWarnCheck = false;
				
				if (ret == 9)
					logonDbInfo.nMonAccCnt += nCurAccCnt;	//일 변경 시 월 카운트는 누적
				if (ret == 10)
					logonDbInfo.nMonAccCnt = 0;	//월 변경시 월 카운트는 초기화
					
				logonDbInfo.nDayAccCnt = 0;	//일,월 변경시 일 카운트는 항상 초기화
				nCurAccCnt = 0;	//일,월 변경시 현재 누적 카운트는 항상 초기화
				memset(szLimitTime,(char)NULL,sizeof(char)*16);
				break;
			default :
				break;
		}
				
		switch (ret)
		{
			case 0 : // 변경 없음
				break;
			case 1 : // 일 서비스 제한
				logPrintS(1,"[INF] daily limit [%d]"	,logonDbInfo.nDayLimitCnt);
				return -1;
			case 2 : // 월 서비스 제한
				logPrintS(1,"[INF] monthly limit [%d]"	,logonDbInfo.nMonLimitCnt);
				return -1;
			case 3 : // 일 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] daily limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]"	,szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				return -1;
			case 4 : // 월 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] monthly limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				return -1;
			case 5 : // 일 알람
				sprintf(szTemp,"[INF] daily limit orver - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				break;
			case 6 : // 월 알람
				sprintf(szTemp,"[INF] monthly limit over - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"%s",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				break;
			case 7 : // 일 임계치 알람
				if (!bDayWarnCheck)
				{
					bDayWarnCheck = true;
					sprintf(szTemp,"[INF] daily limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nDayWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin daily limit msg send failed",0,0);
					}
						
				}
				break;
			case 8 : // 월 임계치 알람
				if (!bMonWarnCheck)
				{
					bMonWarnCheck = true;
					sprintf(szTemp,"[INF] monthly limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nMonWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin monthly limit msg send failed",0,0);
					}
						
				}
				break;
			default :
				break;
		}
	}
	
	return 0;
}


//* < brief 발송 제한 체크
int SenderProcess::LimitCheck(CLogonDbInfo& logonDbInfo)
{
/* return value
	1 : 일 서비스 제한
	2 : 월 서비스 제한
	3 : 일 서비스 제한	+ 알람
	4 : 월 서비스 제한	+ 알람
	5 : 일 알람
	6 : 월 알람
	7 : 일 임계치 알람
	8 : 월 임계치 알람
	9 : 일 변경에 따른 누적 카운트 초기화
	10 : 월 변경에 따른 누적 카운트 초기화
	0 : 변경 없음
*/
	bool bDay=false;
	bool bMon=false;
	int  nDayAccCnt 		= logonDbInfo.nDayAccCnt;
	int  nMonAccCnt 		= logonDbInfo.nMonAccCnt;
	int  nDayWarnCnt 		= logonDbInfo.nDayWarnCnt;
	int  nMonWarnCnt 		= logonDbInfo.nMonWarnCnt;
	int  nDayLimitCnt 		= logonDbInfo.nDayLimitCnt;
	int  nMonLimitCnt 		= logonDbInfo.nMonLimitCnt;
	int  nLimitType 		= atoi(logonDbInfo.szLimitType);
	int  nLimitFlag 		= atoi(logonDbInfo.szLimitFlag);

	//logPrintS(1,"szLimitCurTime(%s)szLimitTime(%s)",szLimitCurTime,szLimitTime);
	if (strncmp(szLimitTime	,szLimitCurTime	,8) != 0)
		bDay = true;	//일 단위
		
	if (strncmp(szLimitTime,szLimitCurTime,6) != 0) 
		bMon = true;	//월 단위

	if (bDay)
	{
		if (bMon)
		{
			return 10;	// 월 변경이 이루어 졌을 경우
		}
		else
		{
			return 9;	// 일 변경이 이루어 졌을 경우
		}
	}
 	//logPrintS(1,"nLimitType(%d),nDayWarnCnt(%d),nCurAccCnt(%d),nDayAccCnt(%d)",nLimitType, nDayWarnCnt, nCurAccCnt,nDayAccCnt);
	//서비스 제한 체크
	switch (nLimitType)
	{
		case 0:	//발송 제한 적용 안함
			return 0;
		case 1:	//일,월 발송 제한
		case 2:	//일 발송 제한
		case 3:	// 월 발송 제한
			if (nLimitType == 1 || nLimitType == 2)
			{
				//일 임계치 체크 (임계치와 제한 건수 사이)
				if (((nDayAccCnt + nCurAccCnt) > nDayWarnCnt) && ((nDayAccCnt + nCurAccCnt) < nDayLimitCnt))
				{
					logPrintS(1,"[INF] daily limit - limit over [%d/%d]", nDayWarnCnt, (nDayAccCnt + nCurAccCnt)-nDayWarnCnt);
					return 7;
				}
				//일 서비스 제한 체크
				if ((nDayAccCnt + nCurAccCnt) > nDayLimitCnt)
				{
					logPrintS(1,"[INF] daily limit - config value [%d]", nDayLimitCnt);
				
					switch (nLimitFlag)
					{
						case 1 :
							return 1;
						case 2 :
							return 3;
						case 3 :
							return 5;
						default :
							return 0;
					}
				}
			}
//logPrintS(1,"nMonWarnCnt(%d),nMonAccCnt(%d),nMonLimitCnt(%d)",nMonWarnCnt, nMonAccCnt+nCurAccCnt+nDayAccCnt, nMonLimitCnt);

			if (nLimitType == 1 || nLimitType == 3)
			{
				//월 임계치 체크 (임계치와 제한 건수 사이)
				if (((nMonAccCnt + nCurAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt) < nMonLimitCnt))
				{
					logPrintS(1,"[INF] monthly limit - limit over [%d/%d]", nMonWarnCnt, (nMonAccCnt + nCurAccCnt)-nMonWarnCnt);
					return 8;
				}
				//월 서비스 제한 체크
				//	if ((nMonAccCnt + nCurAccCnt) > nMonLimitCnt)
				if ((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonLimitCnt)
				{
					logPrintS(1,"[INF] monthly limit - config value [%d]", nMonLimitCnt);
					switch (nLimitFlag)
					{
						case 1 :
							return 2;
						case 2 :
							return 4;
						case 3 :
							return 6;
						default :
							return 0;
					}
				}
			}
		default:
			return 0;
		break;
	}
	return 0;
}

int loadDialCodeAll()
{
	checkCallback.set_dialcode_list_0101.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0101, (char*)"0101") < 0)
		return -1;

	checkCallback.set_dialcode_list_0102.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0102, (char*)"0102") < 0)
		return -1;

	checkCallback.set_dialcode_list_0103.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0103, (char*)"0103") < 0)
		return -1;

	checkCallback.set_dialcode_list_0104.clear();
	if(g_oracle.selectAllowDialCode(checkCallback.set_dialcode_list_0104, (char*)"0104") < 0)
		return -1;

}

int setMMSRPTTBL(int type, long long nMMSId, CMMSPacketSend& mmsPacketSend, int nResCode,char* res_text,char* cid)
{
	int ret;
	int nTelcoFlag = 0;
	char szMBaseId[40+1];
	CSenderDbMMSRPTQUE senderDbMMSRPTQUE;
	/*CMData mData;
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}*/

	memset(&senderDbMMSRPTQUE,0x00,sizeof(CSenderDbMMSRPTQUE));
	
	memset(szMBaseId,0x00,sizeof(szMBaseId));	
    strcpy(szMBaseId,mmsPacketSend.getMBaseIdValue());
    trim(szMBaseId,strlen(szMBaseId));
    
    if(strcmp(szMBaseId,"SS000000") == 0)
    {
    	nTelcoFlag =1;	
    }
    else if(strcmp(szMBaseId,"SL000000") == 0)
    {
		nTelcoFlag =2;	
    }
    else if(strcmp(szMBaseId,"SMwThT00") == 0||strcmp(szMBaseId,"SMwThM00") == 0||
    		strcmp(szMBaseId,"SMwLhX00") == 0||strcmp(szMBaseId,"SMwRhX00") == 0||
    		strcmp(szMBaseId,"CMwMhM0300") == 0||strcmp(szMBaseId,"CMwMhM0400") == 0||
    		strcmp(szMBaseId,"CMwMhM0500") == 0||strcmp(szMBaseId,"CMwMhM0600") == 0||
    		strcmp(szMBaseId,"CMwShS0300") == 0||strcmp(szMBaseId,"CMwShS0400") == 0||
    		strcmp(szMBaseId,"CMwShS0500") == 0||strcmp(szMBaseId,"CMwShS0600") == 0)
    {
    	nTelcoFlag =3;	
    }
    else{
    	nTelcoFlag =1;	//template
    }

	strcpy(senderDbMMSRPTQUE.szDstAddr , "010");
	
	strcpy(senderDbMMSRPTQUE.szPtnSn   , mmsPacketSend.getKeyValue()     );
	senderDbMMSRPTQUE.nMMSId 		= nMMSId;
	senderDbMMSRPTQUE.res_code     = nResCode;
	strcpy(senderDbMMSRPTQUE.res_text   , res_text);
	senderDbMMSRPTQUE.nTelcoId = getTelcoId(gSenderInfo.szSmsTelcoInfo, nTelcoFlag);
	senderDbMMSRPTQUE.nType 		= type;
	senderDbMMSRPTQUE.nTelcoFlag 		= nTelcoFlag;
	memcpy(senderDbMMSRPTQUE.szCid, cid, 10);
	
	
	ret = g_oracle.setSendReportData(senderDbMMSRPTQUE);
	
	if (ret < 0)
	{
		if(type == 1)
		{
			logPrintS(0,"[ERR] setSendReportData TBL MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);
		}else
		{
			logPrintS(0,"[ERR] setSendReportData QUEUE MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);	
		}
	}
	
	return ret;
}

int setMMSCTNRPTTBL(int type, int nCtnId, CMMSPacketSend& mmsPacketSend, int nResCode,char* res_text,char* cid)
{
	int ret;
	int nTelcoFlag = 0;
	CSenderDbCTNMMSRPTQUE senderDbCTNMMSRPTQUE;
	/*CMData mData;
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}*/

	memset(&senderDbCTNMMSRPTQUE,0x00,sizeof(CSenderDbCTNMMSRPTQUE));
	
	nTelcoFlag = 4;

	strcpy(senderDbCTNMMSRPTQUE.szPtnSn   , mmsPacketSend.getKeyValue()     );
	senderDbCTNMMSRPTQUE.nCtnId 		= nCtnId;
	senderDbCTNMMSRPTQUE.res_code     = nResCode;
	strcpy(senderDbCTNMMSRPTQUE.res_text   , res_text);
	memcpy(senderDbCTNMMSRPTQUE.szCid, cid, 10);
	
	senderDbCTNMMSRPTQUE.nType = type;
	
	ret = g_oracle.setSendCtnReportData(senderDbCTNMMSRPTQUE);
	
	if (ret < 0)
	{
		if(type == 1)
		{
			logPrintS(0,"[ERR] setSendCtnReportData TBL CTNID[%d] PtnSn[%s] ret[%d]",nCtnId, senderDbCTNMMSRPTQUE.szPtnSn  , ret);
		}else
		{
			logPrintS(0,"[ERR] setSendCtnReportData QUEUE CTNID[%d] PtnSn[%s] ret[%d]",nCtnId, senderDbCTNMMSRPTQUE.szPtnSn  , ret);	
		}
	}
	
	return ret;
}

int decPassword()
{
	char *sArr[2] = { NULL, };
	char szId[10];
	char szEncPwd[32];
	/* Buffer for the decrypted text */
	char decryptedtext[128];
	unsigned char* decBase64;
	char logMsg[1024];
	
	int encoding_num = 0;
	int decoding_num = 1;
	int decryptedtext_len, ciphertext_len;
	
	int i = 0;
	
	memset(szId,0x00,sizeof(szId));
	memset(szEncPwd,0x00,sizeof(szEncPwd));
	memset(szUid,0x00,sizeof(szUid));
	memset(decryptedtext,0x00,sizeof(decryptedtext));
	memset(logMsg,0x00,sizeof(logMsg));
	
	char *ptr = strtok(gConf.dbuid, "/");      // " " 공백 문자를 기준으로 문자열을 자름, 포인터 반환

  while (ptr != NULL)               // 자른 문자열이 나오지 않을 때까지 반복
  {
  	 //printf("%s\n", ptr);          // 자른 문자열 출력
     sArr[i] = ptr;             // 문자열을 자른 뒤 메모리 주소를 문자열 포인터 배열에 저장
     i++;                       // 인덱스 증가
	
	 ptr = strtok(NULL, "/");   // 다음 문자열을 잘라서 포인터를 반환
  }
  
  strcpy(szId, sArr[0]);
  strcpy(szEncPwd, sArr[1]);
  
  for(int k =2 ; k < i; k++)
  {
  	strcat(szEncPwd, "/");
  	strcat(szEncPwd, sArr[k]);
  }
  
  
  decBase64 = base64_decode((unsigned char *)szEncPwd, strlen ((char *)szEncPwd),&decoding_num);
	
	if(decBase64 == NULL)
	{
		logPrintS(0,"[ERR]decPassword : base64 decryption Error");
		return -1;
	}
	/* Decrypt the ciphertext */
	decryptedtext_len = decrypt((unsigned char*)decBase64, decoding_num, rawkey, iv,
	                            (unsigned char*)decryptedtext);
	
	if(decryptedtext_len < 0){
		logPrintS(0,"[ERR]decPassword : aes256 decryption Error");	
		return -1;
	}
		
	/* Add a NULL terminator. We are expecting printable text */
	decryptedtext[decryptedtext_len] = '\0'; 
	
	strncpy(szUid, szId, strlen(szId));
	strcat(szUid, "/");
	strncat(szUid, decryptedtext, strlen(decryptedtext));
		
	return 0;
}

int GetStringLength(const char *ap_string)
{
	int count = 0;
	
	while(*ap_string)
	{
		if(*ap_string >> 7) ap_string++;
		ap_string++;		
		count++;		
	}
	
	return count;
		
}