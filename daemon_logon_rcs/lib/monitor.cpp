#include "monitor.h"

CMonitor::CMonitor()
{
    /* monidata init */
    memset(type,0x00,sizeof(type));
    memset(thread,0x00,sizeof(thread));
    memset(curdate,0x00,sizeof(curdate));
    memset(processname,0x00,sizeof(processname));
    memset(ospid,0x00,sizeof(ospid));
    memset(connid,0x00,sizeof(connid));
    memset(customptnid,0x00,sizeof(customptnid));
    memset(newconnip,0x00,sizeof(newconnip));
    memset(newstartT,0x00,sizeof(newstartT));
    memset(newdataT,0x00,sizeof(newdataT));
    memset(newlinkT,0x00,sizeof(newlinkT));
    memset(datasum,0x00,sizeof(datasum));

}

int CMonitor::Init(char* type,char* thread,char* process,char* cid,int ptnid,char* connip)
{
    /* monidata init */
    strcpy(this->type,type);
    strcpy(this->thread,thread);
    strcpy(processname,process);
    sprintf(ospid,"%d",getpid());
    strcpy(connid,cid);
    sprintf(customptnid,"%d",ptnid);
    strcpy(newconnip,connip);
    get_timestring("%04d%02d%02d:%02d%02d%02d",
            time(NULL),
            newstartT);
    return 0;
}

void CMonitor::setDataSum(int sum)
{
	sprintf(datasum,"%d",sum); 
}

int CMonitor::setCurDate()
{
    memset(curdate,0x00,sizeof(curdate));
    get_timestring("%04d%02d%02d%02d",time(NULL),
            curdate);
    return 0;
}

int CMonitor::setLinkTime()
{
    memset(newlinkT,0x00,sizeof(newlinkT));
    get_timestring("%04d%02d%02d:%02d%02d%02d",time(NULL),
            newlinkT);
    newlinkT[15] = 0x00;
    return 0;
}

int CMonitor::setDataTime()
{
    memset(newdataT,0x00,sizeof(newdataT));
    get_timestring("%04d%02d%02d:%02d%02d%02d",time(NULL),
            newdataT);
    newdataT[15] = 0x00;
    return 0;
}


int CMonitor::send(char* domainName)
{
    int ret;
    CKSSocket conn;
    ret = conn.connectDomain(domainName);
    if( ret != 0 )
    {
        return -1;
    }
    ret = conn.send((char*)this,sizeof(CMonitor));

    if( ret != sizeof(CMonitor) )
    {
        conn.close();
        return -1;
    }
    conn.close();

    return 0;
}



