COPY=cp
DBSTRING=NEO223
DBPW=kskybDB0955**
PROC=proc
CC=g++
RM=rm
LINT=lint
#CFLAGS = -g -D_DEBUG -lrt
#CFLAGS = -g -lrt -DDEBUG=5
CFLAGS = -g -lrt -std=gnu++03 -Wall -DDEBUG=5

ORG_D=${HOME}/daemon_logon_rcs
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src

EXT_LIB=/user/neorcs/command_rcs/obj/sms_ctrlsub++.o
EXT_INC=/user/neorcs/command_rcs/inc

#EXT_SVC_LIB=/user/neomms/command_svc/obj/sms_ctrlsub++.o
#EXT_SVC_INC=/user/neomms/command_svc/inc
EXT_SVC_LIB=/user/neomms/command_rcs/obj/sms_ctrlsub++.o
EXT_SVC_INC=/user/neomms/command_rcs/inc

INCLUDE = -I$(INC_D) 

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library

ORACLE_VERSION  = 9
ORACLE_INCLUDES = -I$(ORACLE_HOME)/rdbms/demo -I$(ORACLE_HOME)/rdbms/public
ORACLE_LIBS     = -L$(ORACLE_HOME)/lib

GEN_FLAGS    =  -fno-exceptions -fno-rtti -D_REENTRANT=1
GEN_INCLUDES =
GEN_LIBS     = -lorapp -ldl -lclntsh  -lpthread

CRYPTO_LIBS = -lcrypto

BC_FILE_RECEIVER_OBJ = $(OBJ_D)/bcFileReceiver.o \
		    $(OBJ_D)/cust_lib_common.o

LOGON_SESSION_MMSB_OBJ = $(OBJ_D)/logonSessionMMSB.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o

LOGON_SESSION_MMSB_PART_OBJ = $(OBJ_D)/logonSessionMMSB_PART.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o

LOGON_SESSION_MMSBS_OBJ = $(OBJ_D)/logonSessionMMSBS.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcessBS.o\
		    $(OBJ_D)/monitor.o\
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/adminUtil.o

LOGON_SESSION_OBJ = $(OBJ_D)/logonSession.o \
		    $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o

SENDERPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

SENDERMMSPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/monitor.o\
			$(OBJ_D)/checkCallback.o

SENDERMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o\
			$(OBJ_D)/checkCallback.o
		    
SENDERMMSPROCESSDB_KEY_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/dbUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/mmsPacketSend.o\
		    $(OBJ_D)/mmsFileProcess.o\
		    $(OBJ_D)/DatabaseORA_MMS_Key.o\
		    $(OBJ_D)/monitor.o\
			$(OBJ_D)/checkCallback.o

REPORTPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

REPORTMMSPROCESS_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/monitor.o

REPORTMMSPROCESSDB_OBJ = $(OBJ_D)/cust_lib_common.o \
		    $(OBJ_D)/mmsPacketBase.o\
		    $(OBJ_D)/logonUtil.o\
		    $(OBJ_D)/adminUtil.o\
		    $(OBJ_D)/DatabaseORA_MMS.o\
		    $(OBJ_D)/monitor.o

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
ORA_INC = ${ORACLE_HOME}/precomp/public

INCLUDE =   $(PRECOMPPUBLIC) -I$(INC_D) -I$(ORA_INC)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC)

ORALIB = -lclntsh

#LIBS = -lnsl  -lpthread
LIBS = -l:libnsl.so.1 -lpthread



#all: 	logonSession \
#	logonDB \
#	admin \
#	senderRCSDB \
#	reportRCSDB \
#	monitorProcess \
#	adminProcess \
#	senderRCSProcess \
#	reportRCSProcess
	
all: 	logonSession \
	logonDB \
	admin \
	monitorProcess \
	adminProcess \
	senderRCSProcess \
	senderRCSProcKey \
	reportRCSProcess	

logonDB: $(OBJ_D)/logonDB.o $(OBJ_D)/cust_lib_common.o $(OBJ_D)/packetUtil.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS)  $(GEN_FLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig  $(ORACLE_LIBS) $(GEN_LIBS)  ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

infoDB: $(OBJ_D)/infoDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)   $(GEN_FLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig  $(ORACLE_LIBS) $(GEN_LIBS)  ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderDB: $(OBJ_D)/senderDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


senderRCSDB: $(OBJ_D)/senderRCSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportRCSDB: $(OBJ_D)/reportRCSDB.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(ORACLE_INCLUDES) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread  -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

logonSession: $(LOGON_SESSION_OBJ) 
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

monitorProcess: $(OBJ_D)/monitorProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@


adminProcess: $(OBJ_D)/adminProcess.o $(OBJ_D)/cust_lib_common.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksthread -lpthread -lksconfig -lksbase64 ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

#senderRCSProcess: $(SENDERMMSPROCESS_OBJ) $(OBJ_D)/senderRCSProcess.o
#	${CC} $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderRCSProcess: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/jsoncpp.o $(OBJ_D)/senderRCSProcessDB.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderRCSProcessDB: $(SENDERMMSPROCESSDB_OBJ) $(OBJ_D)/jsoncpp.o $(OBJ_D)/senderRCSProcessDB.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderRCSProcKey: $(SENDERMMSPROCESSDB_KEY_OBJ) $(OBJ_D)/jsoncpp.o $(OBJ_D)/senderRCSProcKeyDB.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC} $(CFLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lksbase64 -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

senderProcess: $(SENDERPROCESS_OBJ) $(OBJ_D)/senderProcess.o
	${CC} $(CFLAGS)   $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

#reportRCSProcess: $(REPORTMMSPROCESS_OBJ) $(OBJ_D)/reportRCSProcess.o
#	${CC}  $(CFLAGS)  $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportRCSProcess: $(REPORTMMSPROCESSDB_OBJ) $(OBJ_D)/reportRCSProcessDB.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC}  $(CFLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} $(ORALIB) ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

reportProcess: $(REPORTPROCESS_OBJ) $(OBJ_D)/reportProcess.o $(OBJ_D)/base64.o $(OBJ_D)/aes256_cbc.o 
	${CC}  $(CFLAGS) $(CRYPTO_LIBS) $^ $(EXT_LIB) -I$(EXT_INC) $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket -lksconfig ${LINKFLAGS} ${SQL_INCLUDE} ${PROLDLIBS} ${MATHLIBS} -I${INC_D} -o $(BIN_D)/$@

admin: $(OBJ_D)/admin.o $(OBJ_D)/adminUtil.o
	${CC} $(CFLAGS)  $^ $(LIBD) ${LIBS} -L$(KSLIBRARY_PATH) -lkssocket  -lksconfig ${LINKFLAGS} -I${INC_D} -o $(BIN_D)/$@

$(OBJ_D)/logonDB.o: $(SRC_D)/logonDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS)   $(GEN_FLAGS) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

$(OBJ_D)/infoDB.o: $(SRC_D)/infoDB.cpp
	$(RM) -rf $@
	$(CC) $(CFLAGS)   $(GEN_FLAGS) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(ORACLE_INCLUDES) -c $^

$(OBJ_D)/DatabaseORA_MMS.o: $(LIB_D)/DatabaseORA_MMS.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS.cpp $(OBJ_D)/DatabaseORA_MMS.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS.cpp

$(OBJ_D)/DatabaseORA_MMS_Key.o: $(LIB_D)/DatabaseORA_MMS_Key.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA_MMS_Key.*
	$(COPY) $(LIB_D)/DatabaseORA_MMS_Key.cpp $(OBJ_D)/DatabaseORA_MMS_Key.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES iname=$(OBJ_D)/DatabaseORA_MMS_Key.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS) -o $(OBJ_D)/DatabaseORA_MMS_Key.o ${LINKFLAGS} $(ORALIB) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA_MMS_Key.cpp

$(OBJ_D)/senderRCSProcessDB.o: $(SRC_D)/senderRCSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/senderRCSProcessDB.*
	$(COPY) $(SRC_D)/senderRCSProcessDB.cpp $(OBJ_D)/senderRCSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/senderRCSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderRCSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderRCSProcessDB.cpp

$(OBJ_D)/senderRCSProcKeyDB.o: $(SRC_D)/senderRCSProcKeyDB.cpp
	$(RM) -rf $(OBJ_D)/senderRCSProcKeyDB.*
	$(COPY) $(SRC_D)/senderRCSProcKeyDB.cpp $(OBJ_D)/senderRCSProcKeyDB.pc
	$(PROC) iname=$(OBJ_D)/senderRCSProcKeyDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderRCSProcKeyDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderRCSProcKeyDB.cpp

$(OBJ_D)/reportRCSProcessDB.o: $(SRC_D)/reportRCSProcessDB.cpp
	$(RM) -rf $(OBJ_D)/reportRCSProcessDB.*
	$(COPY) $(SRC_D)/reportRCSProcessDB.cpp $(OBJ_D)/reportRCSProcessDB.pc
	$(PROC) iname=$(OBJ_D)/reportRCSProcessDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/reportRCSProcessDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportRCSProcessDB.cpp

$(OBJ_D)/senderRCSDB.o: $(SRC_D)/senderRCSDB.cpp
	$(RM) -rf $(OBJ_D)/senderRCSDB.*
	$(COPY) $(SRC_D)/senderRCSDB.cpp $(OBJ_D)/senderRCSDB.pc
	$(PROC) iname=$(OBJ_D)/senderRCSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)   -o $(OBJ_D)/senderRCSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/senderRCSDB.cpp
	
$(OBJ_D)/reportDB.o: $(SRC_D)/reportDB.cpp
	$(RM) -rf $(OBJ_D)/reportDB.*
	$(COPY) $(SRC_D)/reportDB.cpp $(OBJ_D)/reportDB.pc
	$(PROC) iname=$(OBJ_D)/reportDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=neorcs/$(DBPW)@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportDB.cpp


$(OBJ_D)/reportRCSDB.o: $(SRC_D)/reportRCSDB.cpp
	$(RM) -rf $(OBJ_D)/reportRCSDB.*
	$(COPY) $(SRC_D)/reportRCSDB.cpp $(OBJ_D)/reportRCSDB.pc
	$(PROC) iname=$(OBJ_D)/reportRCSDB.pc include=$(INC_D) include=$(ORA_INC) include=$(KSLIBRARY_INC) include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP  PARSE=NONE THREADS=YES CTIMEOUT=3 SQLCHECK=FULL userid=neorcs/kskybDB0955**@$(DBSTRING)
	$(RM) -rf tp*
	$(CC) $(CFLAGS)  -o $(OBJ_D)/reportRCSDB.o $(CFLAGS) -I$(ORA_INC) -I$(KSLIBRARY_INC) -I${INC_D} -I$(EXT_INC) -c $(OBJ_D)/reportRCSDB.cpp

$(OBJ_D)/logonSession.o: $(SRC_D)/logonSession.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitorProcess.o: $(SRC_D)/monitorProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/adminProcess.o: $(SRC_D)/adminProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

#$(OBJ_D)/senderRCSProcess.o: $(SRC_D)/senderRCSProcess.cpp
#	$(RM) -rf $@
#	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/senderProcess.o: $(SRC_D)/senderProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/admin.o: $(SRC_D)/admin.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/mmsPacketUtil.o: $(LIB_D)/mmsPacketUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsPacketSend.o: $(LIB_D)/mmsPacketSend.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/mmsFileProcess.o: $(LIB_D)/mmsFileProcess.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/mmsPacketBase.o: $(LIB_D)/mmsPacketBase.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/adminUtil.o: $(LIB_D)/adminUtil.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/monitor.o: $(LIB_D)/monitor.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/reportRCSProcess.o: $(SRC_D)/reportRCSProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/reportProcess.o: $(SRC_D)/reportProcess.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/cust_lib_common.o: $(LIB_D)/cust_lib_common.c
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -c $^

$(OBJ_D)/logonUtil.o: $(LIB_D)/logonUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/dbUtil.o: $(LIB_D)/dbUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^


$(OBJ_D)/packetUtil.o: $(LIB_D)/packetUtil.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^

$(OBJ_D)/checkCallback.o: $(LIB_D)/checkCallback.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^
	
$(OBJ_D)/Curl.o: $(LIB_D)/Curl.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I /usr/include/curl -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^	
	
$(OBJ_D)/jsoncpp.o: $(LIB_D)/jsoncpp.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^	
	
$(OBJ_D)/aes256_cbc.o: $(LIB_D)/aes256_cbc.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) $(CRYPTO_LIBS) -c $^	
	
$(OBJ_D)/base64.o: $(LIB_D)/base64.cpp
	$(RM) -rf $@
	$(CC)  -o $@ $(CFLAGS) -I${INC_D} -I$(EXT_INC) -I$(KSLIBRARY_INC) -c $^		

clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp
